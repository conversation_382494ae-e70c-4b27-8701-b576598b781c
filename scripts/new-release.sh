#!/bin/bash

release_version=$1
lerna_minor_version=$2

echo "current version: $release_version";
echo "current lerna version: $lerna_minor_version";

git checkout develop
git pull origin develop
git checkout -b "release/${release_version}"
npm run prepareRelease --lerna-version="${lerna_minor_version}" --release-version="${release_version}"

RELEASE_VERSION="${release_version}" node scripts/update-swagger-version.js

git add .
git commit -m "Create release ${release_version}"
git push -u origin "release/${release_version}"
