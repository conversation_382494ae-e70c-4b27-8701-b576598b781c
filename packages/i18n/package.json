{"name": "@skywind-group/sw-management-i18n", "version": "2.141.0-develop", "description": "Contains localizations", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p lib/skywind && echo $(node -p \"require('./package.json').version\") $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version"}, "dependencies": {"@skywind-group/sw-wallet-adapter-core": "2.1.9", "i18n": "0.15.1"}, "devDependencies": {"@types/i18n": "0.13.12"}}