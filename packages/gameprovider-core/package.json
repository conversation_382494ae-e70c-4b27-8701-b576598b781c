{"name": "@skywind-group/sw-management-gameprovider-core", "version": "2.141.0-develop", "description": "Contains several interfaces to share between gameprovider and game auth service", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p lib/skywind && echo $(node -p \"require('./package.json').version\") $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version", "only-test": "MEASURES_BASE_INSTRUMENT=false nyc mocha lib/test/**/**/*.spec.js lib/test/**/*.spec.js lib/test/*.spec.js"}, "dependencies": {"@skywind-group/sw-currency-exchange": "2.3.19", "@skywind-group/sw-deferred-payment": "^2.0.0", "@skywind-group/sw-game-provider-ext-game-history": "~3.1.0", "@skywind-group/sw-management-adapters": "workspace:~2.141.0-develop", "@skywind-group/sw-management-deferredpayment": "workspace:~2.141.0-develop", "@skywind-group/sw-management-gameprovider": "workspace:~2.141.0-develop", "@skywind-group/sw-management-playersession": "workspace:~2.141.0-develop", "@skywind-group/sw-management-playservice": "workspace:~2.141.0-develop", "@skywind-group/sw-management-promo-wallet": "workspace:~2.141.0-develop", "@skywind-group/sw-management-wallet": "workspace:~2.141.0-develop", "@skywind-group/sw-messaging": "0.2.4", "@skywind-group/sw-wallet": "1.0.8", "@skywind-group/sw-wallet-adapter-core": "2.1.9", "jsonwebtoken": "9.0.2", "lodash": "4.17.21", "request": "2.88.0"}, "devDependencies": {"@skywind-group/sw-adapter-regulation-support": "^1.0.2", "@types/chai-as-promised": "^7.1.8", "@types/request": "2.48.12", "chai-exclude": "2.0.2", "emitter-listener": "1.1.2", "express-prom-bundle": "7.0.2", "inversify": "5.0.1", "inversify-express-utils": "6.3.2", "inversify-inject-decorators": "3.1.0", "ioredis": "5.5.0", "kafka-node": "5.0.0", "nyc": "15.1.0", "prom-client": "15.0.0", "sequelize": "6.37.7", "uuid": "9.0.1"}}