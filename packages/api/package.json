{"name": "@skywind-group/sw-management-api", "private": true, "version": "2.141.0-develop", "release": "5.57", "description": "", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "index.js", "scripts": {"clean": "rm -rf ./out", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p out/skywind && echo $(node -p \"require('./package.json').release\") $( git log --pretty=format:'%h' -n 1) $(date) > ./out/skywind/version", "only-test": "MEASURES_BASE_INSTRUMENT=false nyc mocha out/test/**/**/*.spec.js out/test/**/*.spec.js out/test/*.spec.js", "test:ts": "MEASURES_BASE_INSTRUMENT=false nyc mocha --require ts-node/register src/test/**/**/*.spec.ts src/test/**/*.spec.ts src/test/*.spec.ts", "test:mocha": "mocha -r ts-node/register -r dotenv/config --exit", "fetch-game-images": "node fetch-game-images.mjs", "dev:mapi": "NODE_OPTIONS=--max-http-header-size=40960 nodemon -r dotenv/config src/skywind/app.ts", "dev:terminal": "nodemon -r dotenv/config src/skywind/appTerminal.ts", "dev:player": "nodemon -r dotenv/config src/skywind/appPlayer.ts", "dev:game-auth": "nodemon -r dotenv/config src/skywind/appGameAuth.ts", "dev:site": "nodemon -r dotenv/config src/skywind/appSite.ts"}, "dependencies": {"@fastify/compress": "8.0.1", "@fastify/cookie": "11.0.2", "@fastify/middie": "9.0.3", "@fastify/static": "8.1.1", "@google-cloud/storage": "7.16.0", "@skywind-group/gelf-stream": "1.2.6", "@skywind-group/sw-adapter-regulation-support": "^1.0.2", "@skywind-group/sw-currency-exchange": "2.3.19", "@skywind-group/sw-deferred-payment": "^2.0.0", "@skywind-group/sw-deferred-payment-cache": "^2.0.0", "@skywind-group/sw-deferred-payment-client": "^2.0.0", "@skywind-group/sw-domain-routing": "3.1.0", "@skywind-group/sw-falcon-oauth": "1.2.2", "@skywind-group/sw-game-provider-ext-game-history": "~3.1.0", "@skywind-group/sw-gameprovider-adapter-core": "~1.3.2", "@skywind-group/sw-live-core": "2.0.6", "@skywind-group/sw-management-adapters": "workspace:~2.141.0-develop", "@skywind-group/sw-management-deferredpayment": "workspace:~2.141.0-develop", "@skywind-group/sw-management-gameprovider": "workspace:~2.141.0-develop", "@skywind-group/sw-management-gameprovider-core": "workspace:~2.141.0-develop", "@skywind-group/sw-management-i18n": "workspace:~2.141.0-develop", "@skywind-group/sw-management-playersession": "workspace:~2.141.0-develop", "@skywind-group/sw-management-playservice": "workspace:~2.141.0-develop", "@skywind-group/sw-management-promo-wallet": "workspace:~2.141.0-develop", "@skywind-group/sw-management-wallet": "workspace:~2.141.0-develop", "@skywind-group/sw-messaging": "0.2.4", "@skywind-group/sw-sm-result-builder": "~0.1.11", "@skywind-group/sw-utils": "2.5.3", "@skywind-group/sw-wallet": "1.0.8", "@skywind-group/sw-wallet-adapter-core": "2.1.9", "agentkeepalive": "^4.5.0", "ajv": "^6.12.0", "append-query": "2.1.1", "authenticator": "1.1.5", "body-parser": "1.20.3", "bole": "5.0.15", "bole-console": "0.1.10", "cls-hooked": "4.2.2", "compression": "1.7.4", "cookie-parser": "1.4.7", "emitter-listener": "1.1.2", "express": "4.21.1", "express-csv": "0.6.0", "express-http-proxy": "2.1.1", "express-mung": "0.5.1", "express-prom-bundle": "7.0.2", "express-validator": "5.3.1", "fastify": "5.3.0", "generic-pool": "3.9.0", "hashids": "^2.3.0", "hashring": "3.2.0", "inversify": "5.0.1", "inversify-express-utils": "6.3.2", "inversify-inject-decorators": "3.1.0", "ioredis": "5.5.0", "is-cidr": "^4.0.2", "js-big-integer": "1.0.2", "json-refs": "3.0.12", "jsonwebtoken": "9.0.2", "kafka-node": "5.0.0", "lodash": "4.17.21", "maxmind": "4.3.22", "method-override": "3.0.0", "node-cache": "5.1.2", "node-mailjet": "3.3.1", "node-schedule": "2.1.1", "pg": "8.14.1", "prom-client": "15.0.0", "qrcode": "1.3.3", "reflect-metadata": "0.2.2", "request": "2.88.0", "sequelize": "6.37.7", "serve-static": "1.16.2", "socket.io": "4.8.1", "socket.io-v2": "npm:socket.io@2.1.1", "superagent": "10.2.3", "swagger-tools": "0.10.1", "to-ico": "^1.1.5", "trek-captcha": "0.4.0", "twilio": "3.29.0", "uuid": "9.0.1", "validator": "^13.9.0"}, "devDependencies": {"@types/chai": "^4.3.11", "@types/chai-as-promised": "^7.1.8", "@types/chai-datetime": "1.0.0", "@types/cls-hooked": "^4.2.0", "@types/express": "4.0.35", "@types/express-http-proxy": "1.6.6", "@types/express-serve-static-core": "4.0.48", "@types/express-validator": "2.20.33", "@types/hashring": "3.2.5", "@types/i18n": "0.13.12", "@types/jsonwebtoken": "^9.0.2", "@types/lodash": "4.17.12", "@types/mocha": "^10.0.8", "@types/node": "22.14.1", "@types/pg": "8.11.11", "@types/request": "2.48.12", "@types/serve-static": "1.15.7", "@types/sinon": "10.0.20", "@types/sinon-chai": "3.2.12", "@types/supertest": "6.0.2", "@types/to-ico": "^1.1.3", "@types/uuid": "9.0.8", "@types/validator": "^13.7.17", "chai": "~4.3.10", "chai-as-promised": "^7.1.1", "chai-datetime": "1.8.1", "chai-shallow-deep-equal": "1.4.4", "socket.io-client": "2.1.1", "factory-girl": "5.0.2", "mocha": "10.7.3", "mocha-typescript": "1.1.12", "nodemon": "^2.0.15", "nyc": "15.1.0", "sinon": "16.1.3", "sinon-chai": "3.7.0", "supertest": "7.0.0", "ts-node": "^10.7.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "packageManager": "pnpm@10.15.0"}