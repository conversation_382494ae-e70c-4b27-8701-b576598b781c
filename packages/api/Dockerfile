######################### SONAR STUFF #########################
FROM asia.gcr.io/gcpstg/lerna-builder:node_22 as build

WORKDIR /app
COPY . /app/

RUN corepack enable && pnpm install --no-frozen-lockfile  \
    && pnpm run clean  \
    && pnpm run compile  \
    && pnpm run version \

CMD ["/bin/cat", "/app/out/skywind/version"]
CMD ["node", "/app/out/skywind/app"]

######################### MAIN IMAGE #########################
FROM node:22.18.0-alpine as main

EXPOSE 3000
WORKDIR /app

COPY --chown=node:node --from=build /app/package.json /app/pnpm-lock.yaml /app/swagger.json /app/swagger-ban-words.json app/swagger-terminal.json /app/swagger-site.json /app/swagger-report-v2.json /app/swagger-report.json /app/swagger-player.json /app/swagger-operator.json /app/swagger-critical-files.json /app/swagger-ehub.json /app/swagger-game-auth.json /app/swagger-mapi-v2.json /app/swagger-live-studio.json /app/.npmrc ./
COPY --chown=node:node --from=build /app/mapi-swagger ./mapi-swagger
COPY --chown=node:node --from=build /app/public ./public
COPY --chown=node:node --from=build /app/resources ./resources
COPY --chown=node:node --from=build /app/swagger-resources ./swagger-resources
COPY --chown=node:node --from=build /app/swagger-ui ./swagger-ui
COPY --chown=node:node --from=build /app/swagger-ui-jpn ./swagger-ui-jpn
COPY --chown=node:node --from=build /app/swagger-ui-player ./swagger-ui-player
COPY --chown=node:node --from=build /app/swagger-ui-site ./swagger-ui-site
COPY --chown=node:node --from=build /app/swagger-ui-v2 ./swagger-ui-v2
COPY --chown=node:node --from=build /app/out ./out

RUN corepack enable \
    && pnpm install --frozen-lockfile --prod \
    && pnpm store prune \
    && rm .npmrc \
    && rm -fr /root/.cache \
    && corepack disable

USER node
CMD ["node", "/app/out/skywind/app"]
