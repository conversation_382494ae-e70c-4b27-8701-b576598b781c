version: '3.5'
services:
  api:
    image: ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}
    build:
      target: main
      context: .
      dockerfile: Dockerfile
    depends_on:
      - sonar
    networks:
      - build_network
  db:
    image: postgres:17-alpine
    command: postgres -c fsync=off -c synchronous_commit=off -c full_page_writes=off
    ports:
      - 5432
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
    networks:
      - build_network
  redis:
    image: redis:alpine
    command: redis-server --save "" --appendonly no
    ports:
      - 6379
    networks:
      - build_network
  sonar:
    image: ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}-sonar
    build:
      target: build
      context: .
      dockerfile: Dockerfile
    links:
      - db
      - redis
    networks:
      - build_network
    environment:
      PGUSER: postgres
      GS_PGUSER: postgres
      PGDATABASE: postgres
      GS_PGDATABASE: postgres
      PG_EXT_HISTORY_USER: postgres
      PG_EXT_HISTORY_DB: postgres
      PG_EXT_HISTORY_USER_SLAVE: postgres
      PG_EXT_HISTORY_DB_SLAVE: postgres
      SONAR_HOST_URL: "${SONAR_HOST_URL}"
    command: sh -c "NODE_OPTIONS=--max-http-header-size=40960 pnpm run only-test"
networks:
  build_network:
    name: ${SERVICE}-${BRANCH_NAME_NORMALIZED}
