import {
    InternalServerError,
    <PERSON><PERSON><PERSON>ad<PERSON>ordsError,
    NicknameMaxSymbolsError,
    NicknameMinSymbolsError,
    NicknameSymbolsError,
    NicknameIdenticalUsernameError,
} from "../errors";
import config from "../config";
import { NICKNAME_MAX_LENGTH, NICKNAME_MIN_LENGTH } from "./common";
import { logging } from "@skywind-group/sw-utils";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { generateBanWordsToken } from "./token";
import * as superagent from "superagent";

const log = logging.logger("validate-nickname");

export const BAN_WORDS_TOKEN = "x-ban-words-token";

export class ValidateNickname {
    private urlPath = "v1/players/validate-nickname/";

    constructor(protected isMerchant = false) {
    }

    public async checkIdenticalUsername(nickname: string, playerCode: string) {
        if (nickname === playerCode) {
            throw new NicknameIdenticalUsernameError(nickname);
        }
    }

    public async checkSymbols(nickname: string): Promise<void> {
        const urlPath = this.urlPath + encodeURI(nickname);
        await this.get(urlPath);
    }

    private async get(urlPath: string): Promise<any> {
        const token = await generateBanWordsToken({});
        const fullUrl = new URL(urlPath, config.banWords.baseUrl).toString();
        try {
            const request = superagent.get(fullUrl)
                .timeout(config.banWords.timeout)
                .set({
                    [BAN_WORDS_TOKEN]: token,
                    "Content-Type": "application/json",
                });
            const response: superagent.Response = await request;
            return this.processResponse(response);
        } catch (error) {
            return this.processResponse(error.response, error);
        }
    }

    private processResponse(response: superagent.Response, error?: Error) {
        if (error && !response) {
            let message: string = "";
            if ((error as AggregateError).errors) {
                const messages = [];
                (error as AggregateError).errors.forEach(e => messages.push(e.message));
                message = messages.join(", ");
            } else {
                message = error.message;
            }
            log.warn("Failed to process request", error);
            throw new InternalServerError(message);
        }

        if (response.statusCode >= 400 && response.statusCode <= 500) {
            log.warn("Response error", response.body);
            throw this.toSwError(response.body);
        }

        return response.body;
    }

    private toSwError(error: SWError): SWError {
        switch (error.code) {
            case 852: return new NicknameSymbolsError();
            case 853: return new NicknameMinSymbolsError(NICKNAME_MIN_LENGTH);
            case 854: return new NicknameMaxSymbolsError(NICKNAME_MAX_LENGTH);
            case 855: return new NicknameBadWordsError();
            default:
                return new InternalServerError(error.message);
        }
    }
}
