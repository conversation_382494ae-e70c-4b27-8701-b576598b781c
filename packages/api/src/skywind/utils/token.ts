import { UserPermissions, UserType } from "../entities/user";
import config from "../config";
import { BaseEntity } from "../entities/entity";
import * as Errors from "../errors";
import { GameHistoryVisualizationDetailsTokenData, RoundHistory } from "../entities/gameHistory";
import { GameTokenData, PlayMode, StartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { token as jwt } from "@skywind-group/sw-utils";
import { ClientPayload } from "../services/gameUrl/getGameURLInfo";
import { getTokenService } from "../services/blocked/tokenService";

export interface AccessTokenData extends UserPermissions {
    userId: number;
    entityId: number;
    username: string;
    sessionId?: string;
    isSuperAdmin?: boolean;
    roles?: number[];
}

export interface GameLauncherTokenData {
    brandId: number;
    request: ClientPayload;
    player?: PlayerShortLoginTokenData;
    isLobby: boolean;
    lobbySessionId?: string;
    gameCode: string;
}

export interface PlayerLoginTokenData {
    playerCode: string;
    brandId: number;
    brand?: BaseEntity;
    sessionId?: string;
    customerSessionId?: string;
    language?: string;
    country?: string;
    gameGroup?: string;
    gameGroupId?: number;
    currency?: string;
    test?: boolean;
    userId?: number;
    finishedAt?: Date;

    merchantCode?: string;
    merchantType?: string;
    gameCode?: string;
    providerGameCode?: string;
    providerCode?: string;
    isExternalTerminal?: boolean;

    [field: string]: any;
}

export interface PlayerShortLoginTokenData {
    code: string;
    currency: string;
    language: string;
    isTest?: boolean;
    gameGroup?: string;
    country?: string;
}

export interface SiteTokenData {
    brandId: number;
    ts: number;
}

export interface TerminalTokenData {
    brandId?: number;
    playerUrl?: string;
    terminalUrl?: string;
    lobbyUrl?: string;
    operatorType?: string;
    accessToken?: string;
    defaultCurrency?: string;
    defaultLanguage?: string;
    env?: string;
}

export interface TwoFATokenData {
    userId: number;
    entityId: number;
    username: string;
    userType?: UserType;
}

export interface ReplayTokenData {
    roundId: string;
    brandId: string;
    currency: string;
    gameCode: string;
}

export interface HistoryServiceTokenData {
    entityId: number;
}

export interface CreatedGameHistoryTokenData {
    token: string;
    ttl: number;
}

export interface RoundRecoveryRequestData {
    gameContextId?: string;
    round: string | RoundHistory;
    force?: boolean;
    reverted?: boolean;
    closeInSWWalletOnly?: boolean;
}

export function generateAccessToken(data: AccessTokenData): Promise<string> {
    return jwt.generate<AccessTokenData>(data, config.accessToken);
}

export function verifyAccessToken(token: string): Promise<AccessTokenData> {
    const accessTokenConfig = config.skipAccessTokenIssuerValidation ? {
        ...config.accessToken,
        issuer: undefined
    } : config.accessToken;
    return jwt.verify<AccessTokenData>(token, accessTokenConfig);
}

export async function generateStartGameToken(
    data: StartGameTokenData, startGameTokenExpiresIn?: number): Promise<string> {

    validatePlayerCode(data.playerCode);
    const tokenConfig = Object.assign({}, config.startGameToken);
    if (startGameTokenExpiresIn) {
        tokenConfig.expiresIn = startGameTokenExpiresIn;
    }
    return jwt.generate<StartGameTokenData>(data, tokenConfig);
}

export function verifyStartGameToken(token: string): Promise<StartGameTokenData> {
    const tokenConfig = config.skipStartGameTokenIssuerValidation ? {
        ...config.startGameToken,
        issuer: undefined
    } : config.startGameToken;
    return jwt.verify<StartGameTokenData>(token, tokenConfig);
}

export async function generateGameLauncherToken(data: GameLauncherTokenData): Promise<string> {
    return jwt.generate<GameLauncherTokenData>(data, config.gameLauncherToken);
}

export async function verifyGameLauncherToken(data: string): Promise<GameLauncherTokenData> {
    return jwt.verify<GameLauncherTokenData>(data, config.gameLauncherToken);
}

export function verifyStartGameTokenWithoutExpiration(token: string): Promise<StartGameTokenData> {
    const { expiresIn, ...configWithoutExpiresIn } = config.startGameToken;
    const tokenConfig = config.skipStartGameTokenIssuerValidation ? {
        ...configWithoutExpiresIn,
        issuer: undefined
    } : configWithoutExpiresIn;
    return jwt.verify<StartGameTokenData>(token, tokenConfig);
}

export async function generateGameToken(data: GameTokenData): Promise<string> {
    const tokenConfig = config.skipGameTokenIssuerValidation ? {
        ...config.gameToken,
        issuer: undefined
    } : config.gameToken;
    return jwt.generate<GameTokenData>(data, tokenConfig);
}

export function verifyGameToken(token: string): Promise<GameTokenData> {
    return jwt.verify<GameTokenData>(token, config.gameToken);
}

export async function generatePlayerLoginToken(data: PlayerLoginTokenData, confToken?: any): Promise<string> {
    return jwt.generate<PlayerLoginTokenData>(data, confToken || config.playerLoginToken);
}

export async function verifyPlayerLoginToken(token: string): Promise<PlayerLoginTokenData> {
    const tokenConfig = config.skipPlayerLoginTokenIssuerValidation ? {
        ...config.playerLoginToken,
        issuer: undefined
    } : config.playerLoginToken;
    return jwt.verify<PlayerLoginTokenData>(token, tokenConfig);
}

export async function generateSiteToken(data: SiteTokenData): Promise<string> {
    return jwt.generate<SiteTokenData>(data, config.siteToken);
}

export async function verifySiteToken(token: string): Promise<SiteTokenData> {
    return jwt.verify<SiteTokenData>(token, config.siteToken);
}

export async function generateTerminalToken(data: TerminalTokenData): Promise<string> {
    return jwt.generate<TerminalTokenData>(data, config.terminalToken);
}

export async function verifyTerminalToken(token: string): Promise<TerminalTokenData> {
    try {
        const data = await jwt.verify<TerminalTokenData>(token, config.terminalToken);
        await getTokenService(data.brandId).tokenExists(token);
        return data;
    } catch (err) {
        if (err instanceof jwt.TokenVerifyException) {
            return Promise.reject(new Errors.TerminalTokenError());
        }
        return Promise.reject(err);
    }
}

export async function verifyInternalToken<T>(token: string): Promise<T> {
    return jwt.verify<T>(token, config.internalServerToken);
}

export function verifyReplayToken(token: string): Promise<ReplayTokenData> {
    return jwt.verify<ReplayTokenData>(token, config.replayToken);
}

export async function generateLiveStudioToken(data: any): Promise<string> {
    return jwt.generate<any>(data, config.liveStudioToken);
}

export async function verifyLiveStudioToken<T>(token: string): Promise<T> {
    return jwt.verify<T>(token, config.liveStudioToken);
}

export async function generateBanWordsToken(data: any): Promise<string> {
    return jwt.generate<any>(data, config.banWordsToken);
}

export async function verifyBanWordsToken<T>(token: string): Promise<T> {
    return jwt.verify<T>(token, config.banWordsToken);
}

export function generateTwoFAToken(data: TwoFATokenData): Promise<string> {
    return jwt.generate<TwoFATokenData>(data, config.twoFAtoken);
}

export function generateHistoryServiceToken(data: HistoryServiceTokenData): Promise<string> {
    return jwt.generate<HistoryServiceTokenData>(data, config.historyService.token);
}

export function verifyTwoFAToken(token: string): Promise<TwoFATokenData> {
    return jwt.verify<TwoFATokenData>(token, config.twoFAtoken);
}

export async function generateGameHistoryVisualizationToken(data: GameHistoryVisualizationDetailsTokenData,
                                                            ttl?: number): Promise<CreatedGameHistoryTokenData> {
    const tokenConfig = { ...config.gameHistoryToken };
    if (ttl) {
        tokenConfig.expiresIn = ttl;
    }
    const token: string = await jwt.generate<GameHistoryVisualizationDetailsTokenData>(data, tokenConfig);
    return {
        token,
        ttl: tokenConfig.expiresIn
    };
}

export function verifyGameHistoryVisualizationToken(token: string): Promise<GameHistoryVisualizationDetailsTokenData> {
    return jwt.verify<GameHistoryVisualizationDetailsTokenData>(token, config.gameHistoryToken);
}

export function generateInternalToken<T extends object>(data: T): Promise<string> {
    return jwt.generate<T>(data, config.internalServerToken);
}

export function generateReplayToken<T extends object>(data: T): Promise<string> {
    return jwt.generate<T>(data, config.replayToken);
}

export async function parseStartGameToken(token: string | object): Promise<StartGameTokenData> {
    if (typeof token === "string") {
        try {
            return await verifyStartGameToken(token as string);
        } catch (err) {
            if (err instanceof jwt.TokenVerifyException) {
                return Promise.reject(new Errors.StartGameTokenError());
            }

            if (err instanceof jwt.TokenExpiredException) {
                return Promise.reject(new Errors.StartGameTokenExpired());
            }
            return Promise.reject(err);
        }
    } else {
        const tokenData = token as any;
        tokenData.playmode = PlayMode.FUN;
        return tokenData;
    }
}

export async function parseStartGameTokenWithoutExpiration(token: string | object): Promise<StartGameTokenData> {
    if (typeof token === "string") {
        try {
            return await verifyStartGameTokenWithoutExpiration(token as string);
        } catch (err) {
            if (err instanceof jwt.TokenVerifyException) {
                return Promise.reject(new Errors.StartGameTokenError());
            }
            return Promise.reject(err);
        }
    } else {
        const tokenData = token as any;
        tokenData.playmode = PlayMode.FUN;
        return tokenData;
    }
}

function validatePlayerCode(playerCode: string) {
    if (typeof playerCode === "string" && (playerCode.includes(":") || playerCode.includes("\\"))) {
        throw new Errors.ValidationError(`Forbidden symbols in playerCode "${playerCode}"`);
    }
}
