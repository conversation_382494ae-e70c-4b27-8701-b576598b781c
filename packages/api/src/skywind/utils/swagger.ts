import { join } from "node:path";
import { readFileSync } from "node:fs";
import * as jsonRefs from "json-refs";
import { lazy } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import config from "../config";

const swaggerTools = require("swagger-tools");

export function loadJson(file: string): any {
    // read swagger json file
    const content = readFileSync(file, "utf8");
    return JSON.parse(content);
}

export async function loadJsonWithRefs(file: string): Promise<any> {
    // read swagger json file and resolve references
    const parsingResult: any = await jsonRefs.resolveRefsAt(file);
    const result: any = parsingResult.resolved;
    /* https://tools.ietf.org/html/draft-pbryan-zyp-json-ref-03 doesn't support multiple refs inside single object, but
    we can extend it using some tricks */
    if (result?.paths?.["$allOf"]) {
        const paths: any[] = result.paths.$allOf;
        delete result.paths.$allOf;
        Object.assign(result.paths, ...paths);
    }
    return result;
}

export async function initializeSwaggerMiddleware(swaggerDoc): Promise<any> {
    return new Promise((resolve, reject) => {
        try {
            swaggerTools.initializeMiddleware(swaggerDoc, function(middleware) {
                resolve(middleware);
            });
        } catch (e) {
            return reject(e);
        }
    });
}

const swagger = lazy(async () => loadJsonWithRefs(join(process.cwd(), "swagger.json")));
const swaggerV2 = lazy(() => loadJson(join(process.cwd(), "swagger-mapi-v2.json")));
const playerSwagger = lazy(() => loadJson(join(process.cwd(), "swagger-player.json")));
const siteSwaggerExt = lazy(() => loadJson(join(process.cwd(), "swagger-site.json")));
const terminalSwagger = lazy(() => loadJson(join(process.cwd(), "swagger-terminal.json")));
const reportSwagger = lazy(() => loadJson(join(process.cwd(), "swagger-report.json")));
const reportSwaggerV2 = lazy(() => loadJson(join(process.cwd(), "swagger-report-v2.json")));
const operatorSwagger = lazy(() => loadJsonWithRefs(join(process.cwd(), "swagger-operator.json")));
const operatorCriticalFiles = lazy(() => loadJsonWithRefs(join(process.cwd(), "swagger-critical-files.json")));
const gameAuthSwagger = lazy(() => loadJson(join(process.cwd(), "swagger-game-auth.json")));
const liveStudioSwagger = lazy(() => loadJson(join(process.cwd(), "swagger-live-studio.json")));
const banWordsSwagger = lazy(() => loadJson(join(process.cwd(), "swagger-ban-words.json")));
const ehubSwagger = lazy(() => loadJsonWithRefs(join(process.cwd(), "swagger-ehub.json")));

export async function apiSwagger(): Promise<any> {
    return swagger.get();
}

export async function apiJpnSwagger(): Promise<any> {
    return new Promise((resolve, reject) => {
        superagent.get(`${config.authGateway.routes["jackpot-api"]}/api-docs`).end((error, response) => {
            if (error) {
                return reject(error);
            }
            response.body.basePath = "/auth-gateway/jackpot-api/api/v2/jpn";
            return resolve(response.body);
        })
    });
}

export function apiSwaggerV2(): Promise<any> {
    return swaggerV2.get();
}

export function apiSwaggerPlayer() {
    return playerSwagger.get();
}

export function apiSwaggerSite() {
    return siteSwaggerExt.get();
}

export function apiSwaggerTerminal() {
    return terminalSwagger.get();
}

export function apiSwaggerReport() {
    return reportSwagger.get();
}

export function apiSwaggerReportV2() {
    return reportSwaggerV2.get();
}

export function apiSwaggerOperator() {
    return operatorSwagger.get();
}

export function apiSwaggerCriticalFiles() {
    return operatorCriticalFiles.get();
}

export function apiSwaggeGameAuth() {
    return gameAuthSwagger.get();
}

export function apiSwaggerLiveStudio() {
    return liveStudioSwagger.get();
}

export function apiSwaggerBanWords() {
    return banWordsSwagger.get();
}

export function apiSwaggerEhub() {
    return ehubSwagger.get();
}

