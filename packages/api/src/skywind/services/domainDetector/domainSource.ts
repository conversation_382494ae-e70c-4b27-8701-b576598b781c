import { Models } from "../../models/models";
import { getStaticDomainPoolService } from "../staticDomainPool";
import EntityCache from "../../cache/entity";

type EntityDomainField = "staticDomainId" | "lobbyDomainId" | "liveStreamingDomainId" | "ehubDomainId";

export interface DomainSource {
    blocked(): Promise<void>;
}

export class PoolDomainSource implements DomainSource {
    constructor(private readonly poolId: number, private readonly domainId: number) {
    }

    public async blocked() {
        await getStaticDomainPoolService().disableDomain(this.poolId, this.domainId);
    }
}

export class EntityDomainSource implements DomainSource {
    constructor(private readonly field: EntityDomainField, private readonly entityId: number) {
    }

    public async blocked() {
        try {
            await Models.EntityModel.update({ [this.field]: null }, { where: { id: this.entityId } });
        } finally {
            EntityCache.reset();
        }
    }
}
