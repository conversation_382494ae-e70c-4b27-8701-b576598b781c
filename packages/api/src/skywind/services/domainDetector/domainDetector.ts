import { DomainSource } from "./domainSource";
import { getTapkingAdapter } from "./tapkingAdapter";
import { DomainDetectorAdapter, DomainStatus } from "./types";

interface DomainDetectorInfo {
    status?: DomainStatus;
    sources?: DomainSource[];
}

export class DomainDetector {
    private readonly domains: Map<string, DomainDetectorInfo> = new Map();

    constructor(private readonly adapter: DomainDetectorAdapter) {
    }

    public async update(domains: Map<string, DomainSource[]>) {
        const list = await this.adapter.list();
        for (const { domain, status } of list) {
            this.domains.set(domain, {
                status,
                sources: this.domains.get(domain)?.sources
            });
        }
        for (const domain of this.domains.keys()) {
            if (!domains.has(domain)) {
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
        for (const [domain, sources] of domains.entries()) {
            if (!this.domains.has(domain)) {
                await this.adapter.register(domain);
                this.domains.set(domain, { status: { accessStatus: "UNKNOWN" }, sources });
            }
        }
        for (const [domain, { status, sources }] of this.domains.entries()) {
            if (status?.accessStatus === "BLOCKED") {
                for (const source of sources ?? []) {
                    await source.blocked();
                    await this.adapter.remove(domain);
                    this.domains.delete(domain);
                }
            }
        }
    }
}

function getAdapter(adapter: string): DomainDetectorAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter();
    }
    throw new Error(`Domain detector adapter ${adapter} is not supported.`);
}

export function getDomainDetector(adapter: string): DomainDetector {
    return new DomainDetector(getAdapter(adapter));
}
