import { Op } from "sequelize";
import { Models } from "../../models/models";
import { EntityStatus } from "../../entities/entity";
import { DomainInfo } from "./types";
import { DomainSource, EntityDomainSource, PoolDomainSource } from "./domainSource";

interface StaticDomainData {
    id: number;
    domain: string;
}

interface StaticDomainPoolData {
    id: number;
    domainDetectorAdapterId: string;
    domains?: Array<StaticDomainData & {
        StaticDomainPoolItem?: {
            isActive?: boolean;
        };
    }>;
}

interface EntityWithDomains {
    id: number;
    staticDomainId?: number;
    lobbyDomainId?: number;
    liveStreamingDomainId?: number;
    ehubDomainId?: number;
    StaticDomainPoolModel: StaticDomainPoolData;
    staticDomain?: StaticDomainData;
    lobbyDomain?: StaticDomainData;
    liveStreamingDomain?: StaticDomainData;
    ehubDomain?: StaticDomainData;
}

type AdapterDomainInfo = Omit<DomainInfo, "domain"> & { domainDetectorAdapterId: string };
type AdapterDomains = Map<string, Map<string, DomainSource[]>>;

export async function loadDomains(): Promise<AdapterDomains> {
    const entities = await Models.EntityModel.findAll({
        where: {
            staticDomainPoolId: {
                [Op.ne]: null
            },
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                on: {
                    id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                },
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainDetectorAdapterId"],
                required: true,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        through: {
                            attributes: ["isActive"]
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.StaticDomainModel,
                as: "staticDomain",
                foreignKey: "staticDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "lobbyDomain",
                foreignKey: "lobbyDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "liveStreamingDomain",
                foreignKey: "liveStreamingDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "ehubDomain",
                foreignKey: "ehubDomainId",
                attributes: ["id", "domain"],
                required: false
            }
        ],
        attributes: ["id", "staticDomainId", "lobbyDomainId", "liveStreamingDomainId", "ehubDomainId"]
    });
    const items = new Map<string, AdapterDomainInfo>();
    for (const entity of entities) {
        toDomains(entity as unknown as EntityWithDomains, items);
    }
    const adapterDomains: AdapterDomains = new Map();
    for (const [domain, { domainDetectorAdapterId, sources }] of items.entries()) {
        const adapterDomain = adapterDomains.get(domainDetectorAdapterId) || new Map();
        adapterDomain.set(domain, [
            ...(adapterDomain.get(domain) || []),
            ...sources
        ]);
        adapterDomains.set(domainDetectorAdapterId, adapterDomain);
    }
    return adapterDomains;
}

function toDomains(entity: EntityWithDomains, items: Map<string, AdapterDomainInfo>): void {
    const { id: entityId, StaticDomainPoolModel: pool, staticDomain, lobbyDomain, liveStreamingDomain, ehubDomain } = entity;

    if (pool.domainDetectorAdapterId) {
        const addDomain = (id: number, domain: string, source: DomainSource) => {
            const existing = items.get(domain);
            items.set(domain, {
                domainDetectorAdapterId: pool.domainDetectorAdapterId,
                sources: [source, ...existing?.sources ?? []]
            });
        };

        if (Array.isArray(pool.domains) && pool.domains.length > 0) {
            for (const domain of pool.domains) {
                const isActive = domain.StaticDomainPoolItem?.isActive ?? true;
                if (isActive) {
                    addDomain(domain.id, domain.domain, new PoolDomainSource(pool.id, domain.id));
                }
            }
        }
        if (staticDomain) {
            addDomain(staticDomain.id, staticDomain.domain, new EntityDomainSource("staticDomainId", entityId));
        }
        if (lobbyDomain) {
            addDomain(lobbyDomain.id, lobbyDomain.domain, new EntityDomainSource("lobbyDomainId", entityId));
        }
        if (liveStreamingDomain) {
            addDomain(liveStreamingDomain.id, liveStreamingDomain.domain, new EntityDomainSource("liveStreamingDomainId", entityId));
        }
        if (ehubDomain) {
            addDomain(ehubDomain.id, ehubDomain.domain, new EntityDomainSource("ehubDomainId", entityId));
        }
    }
}
