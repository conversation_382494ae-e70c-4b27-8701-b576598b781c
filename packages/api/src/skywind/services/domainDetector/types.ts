import { DomainSource } from "./domainSource";

export type AccessStatus = "AVAILABLE" | "BLOCKED" | "UNKNOWN";

export interface DomainStatus {
    accessStatus: AccessStatus;
    lastCheckedAt?: string | null;
}

export interface MonitoredDomain {
    domain: string;
    status: DomainStatus;
}

export interface DomainInfo {
    domain: string;
    sources: DomainSource[];
}

export interface DomainDetectorAdapter {
    register(domain: string): Promise<MonitoredDomain>;
    remove(domain: string): Promise<void>;
    get(domain: string): Promise<MonitoredDomain>;
    list(): Promise<MonitoredDomain[]>;
}
