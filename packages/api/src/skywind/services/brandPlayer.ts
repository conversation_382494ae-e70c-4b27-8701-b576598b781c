import {
    FindOptions as FindDBOptions,
    literal,
    Op,
    Transaction,
    UniqueConstraintError,
    ValidationError,
    WhereOptions
} from "sequelize";
import { PlayerModel } from "../models/player";
import {
    FindOptions,
    Player,
    PlayerAuditInfo,
    PlayerInfo,
    PlayerInfoWithBalance,
    PlayerShortInfo, PlayerStatus,
    UpdateStatusesData
} from "../entities/player";
import { BrandEntity } from "../entities/brand";
import * as Errors from "../errors";
import { GameGroupModel } from "../models/gamegroup";
import { AgentModel as IAgentModel } from "../models/agent";
import { BaseEntity } from "../entities/entity";
import * as SecurityService from "./security";
import { checkBlocked, SECURITY_AUTH_TYPE } from "./security";
import { COMMENT_MAX_LENGTH, DEFAULT_PASSWORD_PATTERN, FORBIDDEN_CURRENCIES } from "../utils/common";
import logger from "../utils/logger";
import { Audit } from "../entities/audit";
import EntitySettingsService, { getEntitiesSettings, getEntitySettings } from "./settings";
import { EntitySettings, MAX_TEST_PLAYERS_DEFAULT } from "../entities/settings";
import { lazy } from "@skywind-group/sw-utils";
import EntityCache from "../cache/entity";
import {
    getPlayerResponsibleGamingService,
    PlayerResponsibleGamingService,
    PlayerResponsibleGamingUpdateSettings
} from "./playerResponsibleGaming";
import { getCurrencyExchange } from "./currencyExchange";
import { PlayerResponsibleGaming, ResponsibleGamingSettingsType } from "../models/playerResponsibleGamingSettings";
import { getMerchantCRUDService } from "./merchant";
import { getGameGroupService } from "./gamegroup";
import { createPlayerSessionFacade } from "./player/playerSessionFacade";
import { PlayerWalletImpl } from "@skywind-group/sw-management-wallet";
import { getRewards } from "./promotions/playerRewardServices";
import { ValidateNickname } from "../utils/validateNickname";
import { getPlayerInfoService } from "./playerInfo";
import { getBrandPlayerValidator } from "./brandPlayerValidator";
import { fetchAuditByCode } from "./blockedPlayer";
import PlayerGameSessionService from "./player/playerGameSessionService";
import config from "../config";
import { getBrandsWithChildren, getParentIds } from "./entity";
import * as FilterService from "./filter";
import { PagingHelper } from "../utils/paginghelper";
import { Models } from "../models/models";
import { cloneDeep, isEqual } from "lodash";
import { AgentAttributes } from "../entities/agent";
import { GameGroupAttributes } from "../entities/gamegroup";
import { encodeEachInObject } from "../utils/publicid";

const log = logger();

const plModel = Models.PlayerModel;
const ggModel = Models.GameGroupModel;
const AgentModel = Models.AgentModel;

const GROUP_ACTION_MAX_ITEMS = 100;

const playerDefaultPasswordValidator = new RegExp(DEFAULT_PASSWORD_PATTERN);

export const sortableKeys = [
    "code",
    "status",
    "firstName",
    "lastName",
    "email",
    "nickname",
    "lastLogin",
    "createdAt",
    "isTest",
    "isVip"
];

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "code",
    "status",
    "firstName",
    "lastName",
    "nickname",
    "email",
    "country",
    "language",
    "currency",
    "gameGroup",
    "lastLogin",
    "createdAt",
    "updatedAt",
    "isTest",
    "isVip"
];

export const DEFAULT_SORT_KEY = "code";
const PLAYER_PERSONAL_INFO_FIELDS = [ "firstName", "lastName", "email", "customData" ];

export interface UpdateData {
    firstName?: string;
    lastName?: string;
    nickname?: string;
    email?: string;
    currency?: string;
    country?: string;
    language?: string;
    gameGroup?: string;
    status?: string;
    isTest?: boolean;
    customData?: object;
    comments?: string;
    deactivatedAt?: string;
    isVip?: boolean;
}

export interface CreateData extends UpdateData{
    code: string;
    password?: string;
    agentDomain?: string;
    responsibleGamingSettings?: PlayerResponsibleGamingUpdateSettings;
    brandId?: number;
}

export class UpdateDataImpl implements UpdateData {
    public firstName: string;
    public lastName: string;
    public nickname: string;
    public email: string;
    public currency: string;
    public country: string;
    public language: string;
    public gameGroup: string;
    public status: string;
    public isTest: boolean;
    public customData: object;
    public comments: string;
    public deactivatedAt: string;
    public isVip: boolean;

    constructor(item: UpdateData = {}) {
        this.firstName = item.firstName;
        this.lastName = item.lastName;
        this.nickname = item.nickname;
        this.email = item.email;
        this.currency = item.currency;
        this.country = item.country;
        this.language = item.language;
        this.gameGroup = item.gameGroup;
        this.status = item.status;
        this.isTest = item.isTest;
        this.customData = item.customData;
        this.comments = item.comments;
        this.deactivatedAt = item.deactivatedAt;
        this.isVip = item.isVip;
    }
}

export class PlayerImpl implements Player {
    public id: number;
    public password: string;
    public salt: string;
    public isPasswordTemp: boolean;
    public brandId: number;
    public code: string;
    public firstName: string;
    public lastName: string;
    public nickname: string;
    public email: string;
    public status: string;
    public currency: string;
    public country: string;
    public language: string;
    public gamegroupId: number;
    public gamegroupName: string;
    public agentId: number;
    public agentTitle: string;
    public agentDomain: string;
    public isTest: boolean;
    public lastLogin: Date;
    public lastAction?: Date;
    public createdAt: Date;
    public updatedAt: Date;
    public deactivatedAt: Date;
    public customData: object;
    public entity: any;
    public auditData: Audit[];
    public selfExclusionCount: number;
    public comments: string;
    public isVip: boolean;

    private readonly walletHolder = lazy(() => new PlayerWalletImpl(this.brandId, this.code, this.currency));

    constructor(item?: PlayerModel,
                brand?: BrandEntity,
                group?: GameGroupModel,
                agent?: IAgentModel) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.code = item.get("code");
        this.password = item.get("password");
        this.salt = item.get("salt");
        this.isPasswordTemp = !!item.get("isPasswordTemp");
        this.firstName = item.get("firstName");
        this.lastName = item.get("lastName");
        this.nickname = item.get("nickname");
        this.email = item.get("email");
        this.brandId = item.get("brandId");
        this.status = item.get("status");
        this.currency = item.get("currency");
        this.country = item.get("country");
        this.language = item.get("language");
        this.gamegroupId = item.get("gamegroupId") || null;
        this.agentId = item.get("agentId");
        this.isTest = item.get("isTest");
        this.lastLogin = item.get("lastLogin");
        this.createdAt = item.get("createdAt");
        this.updatedAt = item.get("updatedAt");
        this.deactivatedAt = item.get("deactivatedAt");
        this.customData = item.get("customData");
        this.selfExclusionCount = item.get("selfExclusionCount") || 0;
        this.comments = item.get("comments") || undefined;
        this.isVip = item.get("isVip");

        this.entity = brand;
        const gamegroup = group || item.get("gamegroup");
        if (gamegroup) {
            this.updateGameGroup(gamegroup.toJSON());
        }

        if (agent) {
            this.updateAgent(agent.toJSON());
        } else {
            agent = item.get("agent");
            if (agent) {
                this.updateAgent(agent.toJSON());
            }
        }
    }

    public get wallet() {
        return this.walletHolder.get();
    }

    public isSuspended(): boolean {
        return this.status === "suspended";
    }

    public async save(transaction?: Transaction): Promise<Player> {
        await plModel.update(this, { where: { id: this.id }, transaction });
        return this;
    }

    public updateGameGroup(gameGroup: GameGroupAttributes | null) {
        this.gamegroupId = gameGroup ? gameGroup.id : null;
        this.gamegroupName = gameGroup ? gameGroup.name : null;
    }

    public updateAgent(agent: AgentAttributes) {
        this.agentId = agent.id;
        this.agentTitle = agent.title;
        this.agentDomain = agent.domain;
    }

    public async toInfo(): Promise<PlayerInfo> {
        const result: PlayerInfo = this.toAuditInfo();

        const entity = this.entity || (await EntityCache.findById(this.brandId));
        const settings = await getEntitySettings(entity.path);
        const defaultGameGroup = await getGameGroupService().findOneDefault(entity, settings);
        result.defaultGameGroup = defaultGameGroup ? defaultGameGroup.get("name") : null;
        result.isBlocked = await checkBlocked(entity.key, this.code);

        const playerInfo = await getPlayerInfoService().getPlayerInfo(this.code, this.brandId);
        result.isPrivateChatBlock = playerInfo.isPrivateChatBlock;
        result.isPublicChatBlock = playerInfo.isPublicChatBlock;
        result.isVip = playerInfo.isVip;
        result.isTracked = playerInfo.isTracked;
        result.nickname = playerInfo.nickname;
        result.hasWarn = playerInfo.hasWarn;
        result.nicknameChangeAttempts = playerInfo.nicknameChangeAttempts;
        result.noBetNoChat = playerInfo.noBetNoChat;

        return result;
    }

    public async toShortInfo(): Promise<PlayerShortInfo> {
        const playerInfo = await getPlayerInfoService()
            .getPlayerInfo(this.code, this.brandId);

        return {
            firstName: this.firstName,
            lastName: this.lastName,
            playerCode: this.code,
            isVip: playerInfo.isVip,
            isTracked: playerInfo.isTracked,
            isPrivateChatBlock: playerInfo.isPrivateChatBlock,
            isPublicChatBlock: playerInfo.isPublicChatBlock,
            nickname: playerInfo.nickname,
            hasWarn: playerInfo.hasWarn,
            nicknameChangeAttempts: playerInfo.nicknameChangeAttempts,
            noBetNoChat: playerInfo.noBetNoChat
        };
    }

    public toAuditInfo(): PlayerInfo {
        const result: PlayerInfo = {
            code: this.code,
            id: this.id,
            status: this.status,
            currency: this.currency,
            country: this.country,
            language: this.language,
            gameGroup: this.gamegroupName || null,
            agentId: this.agentId,
            agentTitle: this.agentTitle,
            agentDomain: this.agentDomain,
            isTest: this.isTest,
            lastLogin: this.lastLogin,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            deactivatedAt: this.deactivatedAt,
            brandId: this.entity ? this.entity.id : this.brandId,
            brandTitle: this.entity ? this.entity.title : undefined,
            firstName: this.firstName || undefined,
            lastName: this.lastName || undefined,
            nickname: this.nickname || undefined,
            email: this.email || undefined,
            customData: this.customData || undefined,
            comments: this.comments,
            isVip: this.isVip
        };

        if (this.auditData) {
            result.auditData = PlayerImpl.toAuditInfos(this.auditData, true);
        }
        if (this.lastAction) {
            result.lastAction = this.lastAction;
        }

        return result;
    }

    public async toInfoWithBalances(): Promise<PlayerInfoWithBalance> {
        const info = await this.toInfo() as PlayerInfoWithBalance;
        info.balances = await this.wallet.getBalances();
        info.rewards = await getRewards(this.brandId, this.code, this.currency);
        info.isOnline = await createPlayerSessionFacade().sessionExists({
            brandId: this.brandId,
            playerCode: this.code
        });
        return info;
    }

    private static toAuditInfo(audit: Audit, ensureEncoded: boolean = false): PlayerAuditInfo {
        let history = audit.history;
        if (ensureEncoded) {
            history = cloneDeep(audit.history);
            encodeEachInObject(history);
        }
        return {
            ts: audit.ts,
            history: history,
            initiatorType: audit.initiatorType,
            initiatorName: audit.initiatorName,
            ip: audit.ip,
            userAgent: audit.userAgent
        };
    }

    private static toAuditInfos(audits: Audit[], ensureEncoded: boolean = false): PlayerAuditInfo[] {
        return audits.map(audit => PlayerImpl.toAuditInfo(audit, ensureEncoded));
    }
}

function assignFields(toObject, fromObject) {
    const anUpdateObject = new UpdateDataImpl();
    for (const key in fromObject) {
        if (!fromObject.hasOwnProperty(key)) {
            continue;
        }
        if (!anUpdateObject.hasOwnProperty(key)) {
            continue;
        }
        toObject[key] = fromObject[key];
    }
}

export default function getPlayerService(): BrandPlayerService {
    return new BrandPlayerServiceImpl();
}

export interface BrandPlayerService {
    findOne(brand: BrandEntity, options: FindOptions): Promise<Player>;

    findOneExtended(brand: BrandEntity, options: FindOptions, fetchAuditData?: boolean, withLastAction?: boolean,
                    includeComments?: boolean): Promise<Player>;

    findOneWithGameGroup(brand: BrandEntity, options: FindOptions): Promise<Player>;

    getLastPlayerGameAction(brand: BrandEntity, playerCode: string): Promise<Date>;

    isSuspended(brand: BrandEntity, options: FindOptions): Promise<boolean>;

    validatePlayerPassword(settings: EntitySettings, password: string): Promise<boolean>;

    validationPasswordError(passwordIsValid: boolean);

    checkNumberOfTestPlayers(brand: BrandEntity, settings?: EntitySettings): Promise<void>;

    findGameGroup(entity: BaseEntity, gameGroupName: string): Promise<GameGroupModel>;

    findAgent(brandId: number, agentDomain: string): Promise<IAgentModel>;

    updateGameGroup(brand: BrandEntity,
                    options: FindOptions,
                    gameGroupName: string): Promise<PlayerInfo>;

    checkDuplicatePlayersInSubtree(code: string, entityPath: string);

    search(entity: BaseEntity,
           query?: WhereOptions<any>,
           includeBalance?: boolean,
           withoutGameGroup?: boolean): Promise<PlayerInfo[] | PlayerInfoWithBalance[]>;

    searchPromoPlayers(brand: BrandEntity, query: WhereOptions<any>): Promise<Player[]>;

    getNumberOfTestPlayers(brandId: number): Promise<number>;

    changePlayerPassword(brand: BaseEntity, newPassword: string, values: object, where: object);

    getPlayer(brandId: number, playerCode: string): Promise<Player>;

    create(entity: BaseEntity, data: CreateData): Promise<Player>;

    update(entity: BaseEntity, code: string, data: UpdateData): Promise<Player>;

    suspend(entity: BaseEntity, code: string, reason?: string): Promise<PlayerInfo>;

    restore(entity: BaseEntity, code: string): Promise<PlayerInfo>;

    changeStatusByIds(entity: BaseEntity, data: UpdateStatusesData): Promise<UpdateStatusesData>;

    unlockLogin(entity: BaseEntity, code: string): Promise<PlayerInfo>;
}

export class BrandPlayerServiceImpl implements BrandPlayerService {
    private validator = new ValidateNickname();

    public async findOne(brand: BrandEntity,
                         options: FindOptions): Promise<Player> {
        const query: WhereOptions = { brandId: { [Op.eq]: brand.id } };
        if (options.code) {
            query["code"] = { [Op.eq]: options.code };
        }

        const item = await plModel.findOne({ where: query });
        if (!item) {
            return Promise.reject(new Errors.PlayerNotFoundError());
        }

        return new PlayerImpl(item, brand);
    }

    public async findOneExtended(brand: BrandEntity,
                                 options: FindOptions,
                                 fetchAuditData: boolean = false,
                                 withLastAction: boolean = false,
                                 includeComments: boolean = true): Promise<Player> {
        const query: WhereOptions = { brandId: { [Op.eq]: brand.id } };
        if (options.code) {
            query["code"] = { [Op.eq]: options.code };
        }
        const attributes = includeComments
            ? { include: [ "comments" ] }
            : undefined;
        const item = await plModel.findOne({
            attributes,
            include: [
                {
                    association: plModel.associations.gamegroup,
                },
                {
                    association: plModel.associations.agent,
                }
            ],
            where: query,
        });
        if (!item) {
            return Promise.reject(new Errors.PlayerNotFoundError());
        }

        const result = new PlayerImpl(
            item,
            brand,
            item.get("gamegroup"),
            item.get("agent")
        );
        if (fetchAuditData) {
            result.auditData = await fetchAuditByCode(brand.id, item.get("code"));
        }
        if (withLastAction) {
            try {
                result.lastAction = await this.getLastPlayerGameAction(brand, result.code);
            } catch (error) {
                // we just return lastAction: undefined if player does not have an active Session
            }
        }
        return result;
    }

    public async findOneWithGameGroup(brand: BrandEntity,
                                      options: FindOptions): Promise<Player> {
        return this.findOneExtended(brand, options, false, false, false);
    }

    public async getLastPlayerGameAction(brand: BrandEntity, playerCode: string): Promise<Date> {
        const ttl: number = await PlayerGameSessionService.find(brand.id, playerCode);
        if (!ttl) {
            return undefined;
        }
        const lastActionSecondsAgo = config.startGameToken.expiresIn - ttl;
        const lastAction: Date = new Date();
        lastAction.setSeconds(lastAction.getSeconds() - lastActionSecondsAgo);
        return lastAction;
    }

    public async isSuspended(brand: BrandEntity, options: FindOptions): Promise<boolean> {
        return brand.isSuspended() ? brand.isSuspended() :
            brand.findPlayer(options).then((player: Player) => player.isSuspended());
    }

    public async validatePlayerPassword(settings: EntitySettings, password: string): Promise<boolean> {
        let passwordIsValid: boolean;
        if (settings.passwordPattern) {
            passwordIsValid = new RegExp(settings.passwordPattern).test(password);
        } else {
            passwordIsValid = playerDefaultPasswordValidator.test(password);
        }
        return passwordIsValid;
    }

    public async validationPasswordError(passwordIsValid: boolean) {
        if (!passwordIsValid) {
            return Promise.reject(new Errors.ValidationPasswordError());
        }
    }

    public async checkNumberOfTestPlayers(brand: BrandEntity, settings?: EntitySettings): Promise<void> {
        settings = settings || (await getEntitySettings(brand.path));
        const numberOfBrandTestPlayers = await this.getNumberOfTestPlayers(brand.id);
        const maxAllowedTestPlayers = settings.maxTestPlayers !== undefined ?
            settings.maxTestPlayers : MAX_TEST_PLAYERS_DEFAULT;

        if (numberOfBrandTestPlayers >= maxAllowedTestPlayers) {
            return Promise.reject(new Errors.NumberOfTestPlayersIsExceeded(maxAllowedTestPlayers));
        }
    }

    public async findGameGroup(entity: BaseEntity,
                               gameGroupName: string): Promise<GameGroupModel> {

        const settings = await getEntitySettings(entity.path);
        const brandId = settings.gameGroupsInheritance ? { [Op.in]: [entity.id, ...getParentIds(entity)] } : entity.id;
        const findOptions: FindDBOptions<any> = {
            where: {
                brandId,
                name: { [Op.eq]: gameGroupName },
            },
        };

        const gameGroupDB = await ggModel.findOne(findOptions);

        if (!gameGroupDB) {
            return Promise.reject(new Errors.GameGroupNotFound());
        }

        return gameGroupDB;
    }

    public async findAgent(brandId: number, agentDomain: string): Promise<IAgentModel> {
        return AgentModel.findOne({
            where: {
                brandId: { [Op.eq]: brandId },
                domain: { [Op.eq]: agentDomain },
            },
        });
    }

    public async updateGameGroup(brand: BrandEntity,
                                 options: FindOptions,
                                 gameGroupName: string): Promise<PlayerInfo> {
        const player: PlayerImpl = await this.findOne(brand, options) as PlayerImpl;
        let gameGroup;
        if (gameGroupName === null || gameGroupName === "null") {
            gameGroup = null;
        } else {
            const gameGroupDB = await this.findGameGroup(brand, gameGroupName);
            gameGroup = gameGroupDB.toJSON();
        }

        player.updateGameGroup(gameGroup);
        await player.save();
        return player.toInfo();
    }

    public async checkDuplicatePlayersInSubtree(code: string, entityPath: string) {
        const duplicatedPlayer = await plModel.findOne({
            where: {
                code
            },
            include: [
                {
                    association: plModel.associations.entity,
                    where: { path: { [Op.like]: entityPath + "%" } },
                    attributes: [ "path" ]
                }
            ]
        });

        if (duplicatedPlayer) {
            return Promise.reject(new Errors.PlayerAlreadyExistError(`code=${code}`));
        }
    }

    public async search(entity: BaseEntity,
                        query?: WhereOptions<any>,
                        includeBalance: boolean = true,
                        withoutGameGroup?: boolean): Promise<PlayerInfo[] | PlayerInfoWithBalance[]> {

        const brands = await getBrandsWithChildren(entity);
        let gameGroupQuery: WhereOptions;
        if (query["gameGroup"]) {
            gameGroupQuery = {
                name: query["gameGroup"],
            };
            delete query["gameGroup"];
        }
        if (withoutGameGroup) {
            query["gamegroupId"] = null;
        }

        query["brandId"] = { [Op.in]: brands.map(brand => brand.id) };

        const sortBy = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

        return PagingHelper.findAsyncAndCountAll(plModel, {
            where: query,
            offset: FilterService.valueFromQuery(query, "offset"),
            limit: FilterService.valueFromQuery(query, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
            include: [
                {
                    association: plModel.associations.gamegroup,
                    where: gameGroupQuery,
                },
                {
                    association: plModel.associations.agent,
                },
            ],
            useMaster: false
        }, async (player) => {
            const brandId = player.get("brandId");
            const brand: BaseEntity = brandId ? await EntityCache.findById(brandId) : undefined;
            const instance = new PlayerImpl(player, brand as BrandEntity);
            if (includeBalance) {
                return instance.toInfoWithBalances();
            }
            return instance.toInfo();
        });
    }

    public async searchPromoPlayers(brand: BrandEntity, query: WhereOptions = {}): Promise<Player[]> {
        query["brandId"] = brand.id;
        query["status"] = PlayerStatus.NORMAL;
        const playersList = await plModel.findAll({ where: query });
        return playersList.map((player) => new PlayerImpl(player));
    }

    public async getNumberOfTestPlayers(brandId: number): Promise<number> {
        return plModel.count({ where: { brandId, isTest: true } });
    }

    public async changePlayerPassword(brand: BaseEntity,
                                      newPassword: string,
                                      values: object = {},
                                      where: object = {}) {
        const { salt, password } = await SecurityService.createSaltAndPassword(newPassword);
        return await plModel.update(
            {
                salt: salt,
                password: password,
                ...values
            },
            {
                where: {
                    brandId: brand.id,
                    ...where
                },
            }
        );
    }

    public async getPlayer(brandId: number, playerCode: string): Promise<Player> {
        const playerInstance = await plModel.findOne({
            where: {
                brandId: brandId,
                code: playerCode,
            }
        });

        if (!playerInstance) {
            return Promise.reject(new Errors.PlayerNotFoundError());
        }

        const player: Player = new PlayerImpl(playerInstance);
        getBrandPlayerValidator().validatePlayerSuspended(player);
        return player;
    }

    public async suspend(entity: BaseEntity,
                         code: string, reason?: string): Promise<PlayerInfo> {
        const playerInfo = await this.changeStatus(entity, code, PlayerStatus.SUSPENDED);
        const playerSessionService = createPlayerSessionFacade();
        await playerSessionService.kill({ brandId: entity.id, playerCode: code, reason });
        return playerInfo;
    }

    public async unlockLogin(entity: BaseEntity, code: string): Promise<PlayerInfo> {
        const player = await this.findOne(entity as BrandEntity, { code });
        await SecurityService.clearBlockedAuthentication(entity.key, code, SECURITY_AUTH_TYPE.PLAYER);
        return player.toInfoWithBalances();
    }

    public async restore(entity: BaseEntity, code: string): Promise<PlayerInfo> {
        return this.changeStatus(entity, code, PlayerStatus.NORMAL);
    }

    private async changeStatus(entity: BaseEntity, code: string, status: string) {
        const player = await this.findOne(entity as BrandEntity, { code });
        player.status = status;
        await player.save();

        return player.toInfoWithBalances();
    }

    public async changeStatusByIds(entity: BaseEntity,
                                   data: UpdateStatusesData): Promise<UpdateStatusesData> {

        if (data.id.length > GROUP_ACTION_MAX_ITEMS) {
            return Promise.reject(new Errors.BulkActionLimitError(GROUP_ACTION_MAX_ITEMS, data.id.length));
        }
        data.id.sort();

        try {
            await plModel.update(
                { status: data.status },
                {
                    where: { brandId: entity.id, id: { [Op.in]: data.id } }
                }
            );
            return data;

        } catch (error) {
            return Promise.reject(new Errors.BulkActionDbError());
        }
    }

    public async update(brand: BrandEntity,
                        playerCode: string,
                        data: UpdateData): Promise<Player> {

        const player: Player = await this.findOne(brand, { code: playerCode });

        getBrandPlayerValidator().validatePlayerSuspended(player);
        await this.validateBrandAndData(brand, data, player);

        const entitySettingsService = new EntitySettingsService(brand);
        const entitySettings: EntitySettings = await entitySettingsService.get();

        this.sanitizeUpdatingData(player, data, entitySettings);

        if (data.email) {
            const playerWithEmail = await plModel.findOne({
                where: {
                    brandId: brand.id,
                    email: data.email,
                    code: { [Op.ne]: playerCode },
                }
            });
            if (playerWithEmail) {
                return Promise.reject(new Errors.EmailAlreadyExistError());
            }
        }

        if (data.nickname) {
            await this.validator.checkIdenticalUsername(data.nickname, playerCode);
        }

        if (data.currency && data.currency !== player.currency) {
            try {
                await this.updateDepositLimit(brand, player, data.currency);
            } catch (error) {
                // if player doesn't have Deposit Limit we just skip error
            }
        }

        assignFields(player, data);

        if (data.gameGroup !== undefined) {
            if (data.gameGroup === player.gamegroupName) {
                delete data.gameGroup;
            } else {
                let gameGroup: GameGroupAttributes;
                if (data.gameGroup === null) {
                    gameGroup = null;
                } else {
                    const gameGroupInstance = await this.findGameGroup(brand,
                        data.gameGroup);
                    gameGroup = gameGroupInstance.toJSON();
                }
                player.updateGameGroup(gameGroup);
            }
        }

        if (!Object.keys(data).length) {
            return Promise.reject(new Errors.PlayerInfoHasNotChanged());
        }

        return player.save();
    }

    private async updateDepositLimit(brand: BrandEntity, player: Player, targetCurrency: string): Promise<void> {
        const respGamingService: PlayerResponsibleGamingService =
            getPlayerResponsibleGamingService(brand);
        const playerResponsibleGamingData: PlayerResponsibleGaming =
            await respGamingService.checkAndGetPlayerResponsibleGamingSettings(player.code);
        const playerSettings = playerResponsibleGamingData.settings;
        const types = Object.keys(playerSettings); // casino and sport_bet
        const playerUpdateSettings: PlayerResponsibleGamingUpdateSettings = {};
        let limitsChanged = false;
        for (const settingsType of types as ResponsibleGamingSettingsType[]) {
            if (playerSettings[settingsType].depositLimit > 0) {
                limitsChanged = true;
                playerSettings[settingsType].depositLimit = (await getCurrencyExchange())
                    .exchange(playerSettings[settingsType].depositLimit, player.currency, targetCurrency);
                playerUpdateSettings[settingsType] = playerSettings[settingsType];
            }
        }
        if (limitsChanged) {
            await respGamingService.updatePlayerResponsibleGamingSettings(player.code, playerUpdateSettings);
        }
    }

    private async validateBrandAndData(brand: BrandEntity,
                                       data: UpdateData | CreateData, player?: Player): Promise<void> {
        if (brand.isMerchant) {
            const merchant = await getMerchantCRUDService().findOne(brand);
            if (!merchant.params.isPromoInternal) {
                return Promise.reject(new Errors.MerchantDoesntSupportError());
            }
        }
        if (brand.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }

        if (data.currency && !brand.currencyExists(data.currency) || data.currency === "BNS") {
            return Promise.reject(new Errors.CurrencyNotInListError(data.currency));
        }

        if (FORBIDDEN_CURRENCIES.includes(data.currency)) {
            return Promise.reject(new Errors.CurrencyNotExistError(data.currency));
        }

        if (data.language && !brand.languageExists(data.language)) {
            return Promise.reject(new Errors.LanguageNotInListError(data.language));
        }

        if (("isTest" in data) && brand.isTest && !data.isTest) {
            return Promise.reject(new Errors.ValidationError(
                "Player should be in test mode according to entity"
            ));
        }

        const entitiesSettings = await getEntitiesSettings(brand.path);
        const brandSettings = entitiesSettings[brand.path];
        if (data.isTest && !(player && player.isTest)) {
            await this.checkNumberOfTestPlayers(brand, brandSettings);
        }

        if (data.comments && data.comments.length > COMMENT_MAX_LENGTH) {
            return Promise.reject(new Errors.ValidationError(
                `comment length should be no more than ${COMMENT_MAX_LENGTH}`
            ));
        }

        if (data.nickname) {
            await this.validator.checkSymbols(data.nickname);
        }

        if ("deactivatedAt" in data && data.deactivatedAt !== null) {
            const deactivatedAt = Date.parse(data.deactivatedAt);
            if (isNaN(deactivatedAt) || deactivatedAt < Date.now()) {
                return Promise.reject(new Errors.ValidationError("deactivatedAt should be a datetime in future"));
            }
        }

        if (!player) {
            const code = (data as CreateData).code;
            await getBrandPlayerValidator().validatePlayerCodes(brand, [ code ], brandSettings);

            if (brandSettings?.isPlayerCodeUniqueInSubtree && code) {
                let headUniqueSubtree: string;

                for (const entityName in entitiesSettings) {
                    if (entitiesSettings.hasOwnProperty(entityName)) {
                        const isPlayerCodeUniqueInSubtree = entitiesSettings[entityName]?.isPlayerCodeUniqueInSubtree;
                        if (isPlayerCodeUniqueInSubtree && !headUniqueSubtree) {
                            headUniqueSubtree = entityName;
                        }
                    }
                }
                return this.checkDuplicatePlayersInSubtree(code, headUniqueSubtree);
            }
        }
    }

    private sanitizeData(data: UpdateData | CreateData, entitySettings: EntitySettings): CreateData {
        if (data.email) {
            data.email = data.email.toLowerCase();
        }

        if (entitySettings.playerPrefix) {
            (data as CreateData).code = `${entitySettings.playerPrefix}_${(data as CreateData).code}`;
        }

        if (!entitySettings || !entitySettings.storePlayerInfo) {
            PLAYER_PERSONAL_INFO_FIELDS.forEach(field => delete data[field]);
        }
        return data as CreateData;
    }

    private sanitizeUpdatingData(player: Player, data: UpdateData, entitySettings: EntitySettings) {
        this.sanitizeData(data, entitySettings);
        for (const propToUpdate in data) {
            if (!data.hasOwnProperty(propToUpdate)) {
                continue;
            }
            if (isEqual(player[propToUpdate], data[propToUpdate])) {
                delete data[propToUpdate];
            }
        }
        if (!Object.keys(data).length) {
            throw new Errors.PlayerInfoHasNotChanged();
        }
    }

    public async create(brand: BrandEntity, data: CreateData): Promise<Player> {
        this.setDefaultValues(brand, data);
        await this.validateBrandAndData(brand, data);

        await this.validateResponsibleGaming(brand, data);
        const entitySettingsService = new EntitySettingsService(brand);
        const entitySettings: EntitySettings = await entitySettingsService.get();

        data = this.sanitizeData(data, entitySettings);

        const player: PlayerImpl = new PlayerImpl();

        player.isPasswordTemp = entitySettings?.isPlayerPasswordChangeEnabled;

        Object.assign(player, data);

        const [ gameGroupDB, agentDB ] = await this.setPasswordAndRelatedObjects(brand, player, data);

        const item = await this.createPlayer(player);

        if (data.responsibleGamingSettings) {
            await getPlayerResponsibleGamingService(brand).updatePlayerResponsibleGamingSettings(
                player.code, data.responsibleGamingSettings);
        }

        return new PlayerImpl(
            item,
            brand,
            gameGroupDB,
            agentDB
        );
    }

    private setDefaultValues(brand: BrandEntity, data: CreateData) {
        data.country = data.country || brand.defaultCountry;
        data.currency = data.currency || brand.defaultCurrency;
        data.language = data.language || brand.defaultLanguage;
        data.brandId = brand.id;
        data.status = PlayerStatus.NORMAL;
        data.isTest = typeof data.isTest === "boolean" ? data.isTest : brand.isTest;
    }

    public async validateResponsibleGaming(brand: BrandEntity, data: CreateData): Promise<void> {
        if (data.responsibleGamingSettings) {
            const playerResponsibleGamingService = getPlayerResponsibleGamingService(brand);
            await playerResponsibleGamingService.validateSettings(data.responsibleGamingSettings);
            const entityRespGamingSettings =
                await playerResponsibleGamingService.getEntityResponsibleGamingSettings(brand);
            await playerResponsibleGamingService.checkEntityHasResponsibleGamingEnabled(entityRespGamingSettings);
        }
    }

    private async setPasswordAndRelatedObjects(brand: BrandEntity,
                                               player: PlayerImpl,
                                               data: CreateData): Promise<[ GameGroupModel, IAgentModel ]> {
        if (data.password) {
            const settings: EntitySettings = await getEntitySettings(brand.path);
            const passwordIsValid = await this.validatePlayerPassword(settings, data.password);
            await this.validationPasswordError(passwordIsValid);
            const { salt, password } = await SecurityService.createSaltAndPassword(data.password);
            player.salt = salt;
            player.password = password;
        }

        let gameGroupDB: GameGroupModel = null;
        if (data.gameGroup) {
            gameGroupDB = await this.findGameGroup(brand, data.gameGroup);
        }

        if (gameGroupDB) {
            player.updateGameGroup(gameGroupDB.toJSON());
        }

        let agentDB: IAgentModel = null;
        if (data.agentDomain) {
            agentDB = await this.findAgent(brand.id, data.agentDomain);
            if (agentDB && agentDB.get("id")) {
                player.updateAgent(agentDB.toJSON());
            } else {
                log.warn(data, `Not found agent domain "${data.agentDomain}" in brand "${brand.name}"`);
            }
        }

        return [ gameGroupDB, agentDB ];
    }

    private async createPlayer(player: PlayerImpl): Promise<PlayerModel> {
        try {
            if (player.nickname && player.nickname === player.code) {
                return Promise.reject(new Errors.NicknameIdenticalUsernameError(player.nickname));
            }

            const createdPlayer = await plModel.findOne({
                where: {
                    brandId: player.brandId,
                    [Op.or]: [
                        {
                            code: { [Op.eq]: player.code }
                        },
                        {
                            email: { [Op.eq]: player.email }
                        },
                        {
                            nickname: { [Op.eq]: player.nickname }
                        }
                    ]
                }
            });
            if (createdPlayer) {
                if (createdPlayer.get("code") === player.code) {
                    return Promise.reject(new Errors.PlayerAlreadyExistError(`code=${player.code}`));
                }
                if (player.email && createdPlayer.get("email") === player.email) {
                    return Promise.reject(new Errors.EmailAlreadyExistError());
                }
            }
            return await plModel.create(player);
        } catch (err) {
            if (err instanceof UniqueConstraintError || err instanceof ValidationError) {
                const error = err as UniqueConstraintError | ValidationError;
                log.error(error instanceof UniqueConstraintError ? error.parent : error, "Create player error");
                const paths = error.errors && error.errors[0] && error.errors.map(anError => anError.path);
                if (paths.includes("code")) {
                    return Promise.reject(new Errors.PlayerAlreadyExistError(`code=${player.code}`));
                }
                if (paths.includes("email")) {
                    return Promise.reject(new Errors.EmailAlreadyExistError());
                }
                if (paths.includes("nickname")) {
                    return Promise.reject(new Errors.NicknameAlreadyExistError());
                }
                return Promise.reject(new Errors.PlayerAlreadyExistError());
            }
            log.error(err, "Create player error");
            // continue with the unknown error
            return Promise.reject(err);
        }
    }
}

const brandPlayerServiceImpl = lazy(() => new BrandPlayerServiceImpl());

export const getBrandPlayerService = () => brandPlayerServiceImpl.get();
