import { Merchant, MerchantInfo, MerchantParams, } from "../entities/merchant";
import { BrandEntity } from "../entities/brand";
import { MerchantDBInstance } from "../models/merchant";
import { CreateOptions, DestroyOptions, Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import * as Errors from "../errors";
import {
    AnyMerchantAdapter,
    lookupAdapter,
    MerchantGameFinalizeRequest,
    MerchantGameHistoryDetailsImageRequest,
    MerchantGameHistoryRequest,
    MerchantPlayerKillSessionInternalRequest,
    MerchantResolveRoundRequest
} from "@skywind-group/sw-management-adapters";
import { EntityGame, Game, PlayerGameURLInfo } from "../entities/game";
import { BaseEntity, ChildEntity, EntityInfo, Includable } from "../entities/entity";
import { PlayerInfoWithBalance } from "../entities/player";
import { getChildEntities, getChildIds, getMerchantEntities } from "./entity";
import { generatePlayerLoginToken, PlayerLoginTokenData, PlayerShortLoginTokenData } from "../utils/token";
import { lazy, measures } from "@skywind-group/sw-utils";
import { ProxyDBInstance } from "../models/proxy";
import { ProxyService } from "./proxy";
import EntityCache from "../cache/entity";
import {
    ExternalForceFinishService,
    ExternalHistoryService,
    WinBetHistoryResponseItem
} from "@skywind-group/sw-game-provider-ext-game-history";

import * as HistoryService from "../history/gameHistory";
import {
    GameHistorySpinDetails,
    getSmResultWithExtraData,
    getSpinListByBrand,
    roundHistoryQueryKeysForRoundIdQueries
} from "../history/gameHistory";
import {
    ForceFinishRoundResponse,
    GameHistoryVisualisation,
    GHVisualizationOptions,
    RoundHistory,
    StartFinalizeResponse,
    UnfinishedRoundHistory
} from "../entities/gameHistory";
import { UnfinishedRoundManagementServiceFactory } from "../history/unfinishedRoundManagementService";
import { IChangeInfo, IExternalTransaction } from "@skywind-group/sw-wallet";
import {
    OPERATION_ID,
    PLAYER,
    PlayerWalletImpl,
    WALLET_TRX_TYPE,
    WalletFacade
} from "@skywind-group/sw-management-wallet";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { getRoundHistoryServiceFactory } from "../history/gameHistoryServiceFactory";
import {
    AdditionalInfo,
    MerchantGameInitRequest,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantPageRequest,
    MerchantPageResponse,
    MerchantStartGameTokenData,
    PlayMode,
    SmResultExtraData
} from "@skywind-group/sw-wallet-adapter-core";
import { validatePlaymode } from "../utils/validatePlaymode";
import { getEntityGame } from "./entityGameService";
import logger from "../utils/logger";
import { getGameGroupService } from "./gamegroup";
import * as MerchantCache from "../cache/merchant";
import * as GameService from "./game";
import EntitySettingsService, { getEntitySettings } from "./settings";
import { createPlayerSessionFacade, PlayerSessionFacade } from "./player/playerSessionFacade";
import { getRewards } from "./promotions/playerRewardServices";
import { WALLET_OPERATION_NAME } from "@skywind-group/sw-management-playservice";
import { MerchantTypeImpl } from "./merchantType";
import * as MerchantTypesCache from "../cache/merchantTypes";
import { defaultOperatorDetailsRepository } from "./gameauth/defaultOperatorInfoRepository";
import { defaultOperatorInfoUpdater } from "./gameauth/defaultOperatorInfoUpdater";
import { parseFilter } from "./filter";
import { filterObjectFields, mergeArrayToObject } from "../utils/common";
import { getPlayerInfoService } from "./playerInfo";
import { getGameURLInfo } from "./gameUrl/getGameURLInfo";
import { createFinalizeService } from "../history/unfinishedRoundFinalizeService";
import { getGame } from "./gameprovider";
import envConfig from "../config";
import config from "../config";
import { CountrySource, getMerchantCountrySource } from "../utils/countrySource";
import { validatePlayerCountryRestrictions } from "../utils/validateCountriesRestrictions";
import { sendPlayerBlockingAnalytics } from "./playerBlockingAnalyticsService";
import { createBetWinHistoryService } from "@skywind-group/sw-management-gameprovider-core";
import { sequelize } from "../storage/db";
import { UnfinishedRoundsQuery } from "../history/unfinishedRoundsHistoryService";
import { Models } from "../models/models";
import { AsyncReturnType } from "../entities/typeUtils";
import { decodeId, encodeId } from "../utils/publicid";
import { Proxy } from "../entities/proxy";
import Measurable = measures.Measurable;

const log = logger("get-merchant-game-url");

const DEFAULT_PARAMS: MerchantParams = { supportTransfer: true, keepAliveSec: 30 };
const DEFAULT_PARAMS_FOR_SEAMLESS: MerchantParams = { ...DEFAULT_PARAMS, sameUrlForTerminalLoginAndTicket: true };

const MerchantModel = Models.MerchantModel;
const ProxyModel = Models.ProxyModel;
const EntityModel = Models.EntityModel;
const PlayerModel = Models.PlayerModel;
const LobbyModel = Models.LobbyModel;

export interface CreateData {
    type: string;
    code: string;
    params: MerchantParams;
    isTest?: boolean;
    proxyId?: number;
}

export interface UpdateData {
    params?: MerchantParams;
    code?: string;
    type?: string;
    proxyId?: number;
    lastTestPassing?: Date;
    isTest?: boolean;
}

export interface UpdateDataOptions {
    allowToUpdateIsTest?: boolean; // merchant and entities isTest must have the same values
}

export type GameHistorySpinExtended = GameHistorySpinDetails & { state?: string };

export type RoundHistoryExtended =
    RoundHistory
    & { spins?: GameHistorySpinExtended[] }
    & { smResult: string, smResultExtraData: SmResultExtraData };

export class MerchantImpl implements Merchant, Measurable {

    public brandKey: string;
    public brandId: number;
    public brandTitle: string;
    public type: string;
    public code: string;
    public params: MerchantParams;
    public isTest: boolean;
    public proxyId: number;
    public proxy: Proxy;
    public lastTestsPassing: Date;
    public version: number;

    constructor(item?: MerchantDBInstance, baseEntity?: BaseEntity) {
        if (!item) {
            return;
        }

        this.brandId = item.get("brandId");
        this.type = item.get("type");
        this.code = item.get("code");
        this.params = item.get("params");
        this.isTest = item.get("isTest");
        this.proxyId = item.get("proxyId");
        this.lastTestsPassing = item.get("lastTestsPassing");
        this.version = item.get("version") || 0;

        const entity = item.get("entity");
        if (entity) {
            this.brandKey = entity.get("key");
        }

        const proxy = item.get("proxy") as ProxyDBInstance;
        if (proxy) {
            this.proxy = proxy.toInfo();
        }

        if (baseEntity) {
            this.brandTitle = baseEntity.title;
        }
    }

    public async getAdapter(useMainDomainUrl?: boolean): Promise<AnyMerchantAdapter> {
        const merchantType: MerchantTypeImpl = await MerchantTypesCache.findOne(this.type);
        return lookupAdapter({
            type: this.type,
            typeDetails: merchantType,
            info: this.toInfo(),
            useMainDomainUrl
        });
    }

    public toInfo(): MerchantInfo {
        const info: MerchantInfo = {
            type: this.type,
            code: this.code,
            params: this.params,
            isTest: this.isTest,
            lastTestsPassing: this.lastTestsPassing,
            brandId: this.brandId
        };

        if (this.proxy) {
            info.proxy = this.proxy;
        }

        if (this.proxyId) {
            info.proxyId = this.proxyId;
        }

        if (this.brandTitle) {
            info.brandTitle = this.brandTitle;
        }

        return info;
    }

    public async createGameUrl(gameCode: string,
                               entityGame: EntityGame,
                               initRequest: MerchantGameInitRequest): Promise<MerchantGameURLInfo> {

        initRequest.externalGameId = entityGame.externalGameId || undefined;

        const result = await (await this.getAdapter(true)).createGameUrl(
            this.toInfo(),
            gameCode,
            entityGame.game.gameProvider.code,
            entityGame.game.providerGameCode,
            initRequest
        );

        if (result.tokenData && initRequest.playmode === PlayMode.FUN) {
            result.tokenData.playerCode = result.tokenData.playerCode || `player_${+Date.now()}`;
        }

        return result;
    }

    public async getGameTokenInfo(startToken: MerchantStartGameTokenData,
                                  currency: string,
                                  entityGame: EntityGame,
                                  deviceId?: string,
                                  additionalInfo?: AdditionalInfo): Promise<MerchantGameTokenInfo> {
        const transferEnabled = !!(this.params.supportTransfer && entityGame.game.features.transferEnabled);

        const gameToken = await (await this.getAdapter(true)).getGameTokenInfo(
            this.toInfo(),
            startToken,
            currency,
            transferEnabled,
            additionalInfo);

        if (deviceId) {
            gameToken.gameTokenData.deviceId = deviceId;
        }

        return gameToken;
    }

    public getMeasureKey(): string {
        return this.type + ":" + this.code;
    }
}

const merchantCRUDService = lazy<MerchantCRUDService>(() => new MerchantCRUDService());
export const getMerchantCRUDService = () => merchantCRUDService.get();

class MerchantCRUDService implements Includable<{ merchant: MerchantInfo }> {
    private readonly model: typeof MerchantModel;
    private readonly merchantBooleanParams: string[];

    constructor() {
        this.model = MerchantModel;
        this.merchantBooleanParams = [
            "isPromoInternal",
            "supportTransfer",
            "supportPlayMoney",
            "walletPerGame",
            "refreshSessionForNewGame"
        ];
    }

    public async create(brand: BrandEntity, data: CreateData, transaction?: Transaction): Promise<Merchant> {
        const merchant: MerchantImpl = new MerchantImpl();

        merchant.brandId = brand.id;
        merchant.brandTitle = brand.title;
        merchant.type = data.type;
        merchant.code = data.code;
        merchant.params = (!data.params || data.params.supportTransfer === undefined) ?
            { ...this.getDefaultMerchantParamsForCreate(data.type), ...data.params } : data.params;
        merchant.isTest = brand.isTest;
        merchant.proxyId = data.proxyId;

        await this.validateMerchant(merchant);

        try {
            const createOptions: CreateOptions = {};
            if (transaction) {
                createOptions.transaction = transaction;
            }

            const item: MerchantDBInstance = await this.model.create(merchant, createOptions);
            return new MerchantImpl(item, brand);
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                throw new Errors.MerchantAlreadyExistError();
            }

            throw err;
        }
    }

    private getDefaultMerchantParamsForCreate(merchantType: string): MerchantParams {
        if (merchantType === "seamless") {
            return DEFAULT_PARAMS_FOR_SEAMLESS;
        }
        return DEFAULT_PARAMS;
    }

    public async update(brand: BrandEntity, data: UpdateData, options?: UpdateDataOptions): Promise<Merchant> {
        try {
            const merchant: Merchant = await this.findOne(brand);

            if (Object.keys(data).length === 0) {
                // In case when nothing to update
                return merchant;
            }

            this.patchMerchantInstance(merchant, data, options);

            await this.validateMerchant(merchant);

            const oldVersion = merchant.version;
            merchant.version += 1;
            const [updateCount] = await MerchantModel.update(merchant,
                { where: { brandId: brand.id, version: oldVersion } });
            if (updateCount !== 1) {
                throw new Errors.OptimisticLockException();
            }

            return this.findOne(brand);
        } finally {
            MerchantCache.reset();
            await this.notifyMerchantUpdate(brand);
        }
    }

    private async notifyMerchantUpdate(brand: BrandEntity) {
        try {
            await defaultOperatorInfoUpdater.get()
                .update(await defaultOperatorDetailsRepository.get().findById(brand.id));
        } catch (err) {
            log.error(err, "Failed to notify merchant update");
        }
    }

    public async updateOrCreate(brand: BrandEntity, data: UpdateData): Promise<Merchant> {
        try {
            if (brand.isMerchant) {
                return this.update(brand, data);
            }
            const merchantItem = await this.model.findOne({ where: { brandId: brand.id } });
            if (merchantItem) {
                return this.update(brand, data);
            }

            if (!data.type || !data.code) {
                throw new Errors.ValidationError("type and code are mandatory params");
            }

            if (!data.params || !data.params["password"]) {
                throw new Errors.ValidationError("Password should be not empty");
            }

            const createData: CreateData = {
                type: data.type,
                code: data.code,
                params: data.params,
                proxyId: data.proxyId
            };

            return this.create(brand, createData);
        } finally {
            MerchantCache.reset();
        }
    }

    public patchMerchantInstance(merchant: Merchant,
                                 data: UpdateData,
                                 options: UpdateDataOptions = {}): Merchant {

        merchant.params = filterObjectFields<MerchantParams>(
            mergeArrayToObject([merchant.params, data.params]));
        merchant.code = data.code || merchant.code;
        merchant.type = data.type || merchant.type;
        merchant.lastTestsPassing = data.lastTestPassing || merchant.lastTestsPassing;

        if (options.allowToUpdateIsTest) {
            merchant.isTest = typeof data.isTest === "boolean" ? data.isTest : merchant.isTest;
        }

        // We should delete proxyId from merchant if client send NULL
        if (data.proxyId === null) {
            merchant.proxyId = null;
        } else {
            merchant.proxyId = data.proxyId || merchant.proxyId;
        }

        return merchant;
    }

    public async remove(brand: BrandEntity, transaction?: Transaction): Promise<void> {
        try {
            const destroyOptions: DestroyOptions = { where: { brandId: brand.id } };
            if (transaction) {
                destroyOptions["transaction"] = transaction;
            }
            await MerchantModel.destroy(destroyOptions);
        } finally {
            MerchantCache.reset();
        }
    }

    public async findOne(brand: BaseEntity, raiseErrorIfNotFound: boolean = true): Promise<MerchantImpl> {
        return this.findMerchant({ brandId: brand.id }, raiseErrorIfNotFound, brand);
    }

    public async findOneByTypeAndCode(
        merchantType: string,
        merchantCode: string,
        raiseErrorIfNotFound: boolean = true
    ): Promise<MerchantImpl> {
        return this.findMerchant({ type: merchantType, code: merchantCode }, raiseErrorIfNotFound);
    }

    private async findMerchant(
        where: WhereOptions<any>,
        raiseErrorIfNotFound: boolean = true,
        brand?: BaseEntity
    ): Promise<MerchantImpl> {
        const item: MerchantDBInstance = await this.model.findOne({
            where: where,
            include: [ProxyModel]
        });

        if (!item) {
            if (raiseErrorIfNotFound) {
                return Promise.reject(new Errors.MerchantNotFoundError());
            } else {
                return undefined;
            }
        }
        return new MerchantImpl(item, brand);
    }

    private async validateMerchant(merchant: Merchant) {
        const adapter: AnyMerchantAdapter = await merchant.getAdapter();
        if (!adapter) {
            throw new Errors.MerchantTypeNotSupportedError(merchant.type);
        }
        const validationErrors = [];
        if (merchant.params.keepAliveSec !== undefined && !Number.isFinite(merchant.params.keepAliveSec)) {
            validationErrors.push("Invalid value keepAliveSec");
        }

        for (const merchantBooleanParamName of this.merchantBooleanParams) {
            if (merchant.params[merchantBooleanParamName] !== undefined &&
                typeof merchant.params[merchantBooleanParamName] !== "boolean") {
                validationErrors.push(`Invalid value ${merchantBooleanParamName}`);
            }
        }

        if (merchant.params.walletPerGame && !merchant.params.supportTransfer) {
            validationErrors.push("walletPerGame is allowed only when supportTransfer is true");
        }

        if (merchant.proxyId) {
            const service = new ProxyService();
            await service.retrieve(merchant.proxyId);
        }

        if (validationErrors.length) {
            throw new Errors.ValidationError(validationErrors);
        }
    }

    public async includeTo(entity: EntityInfo): Promise<EntityInfo & { merchant: MerchantInfo }> {
        const merchantEntities = getMerchantEntities(entity);

        const merchants = await this.model.findAll({
            where: {
                brandId: {
                    [Op.in]: merchantEntities.map(({ id }) => id)
                }
            }
        });

        for (const instance of merchants) {
            const info = new MerchantImpl(instance).toInfo();
            const merchantEntity = merchantEntities.find(({ id }) => id === info.brandId);
            (merchantEntity as EntityInfo & { merchant: MerchantInfo }).merchant = info;
        }

        return entity as EntityInfo & { merchant: MerchantInfo };
    }
}

const merchantSearchService = lazy<MerchantSearchService>(() => new MerchantSearchService());
export const getMerchantSearchService = () => merchantSearchService.get();

class MerchantSearchService {

    public async findOneByTypeAndCode(type: string | string [], code: string): Promise<MerchantImpl> {
        const query: WhereOptions<any> = {
            code
        };
        const types = Array.isArray(type) ? type : type.split(",").map(t => t.trim());
        // TODO: maybe it is bug here
        query.type = types.length > 1 ? { [Op.in]: types } : type;

        const item: MerchantDBInstance = await MerchantModel.findOne({
            where: query,
            include: [ProxyModel]
        });

        if (!item) {
            return Promise.reject(new Errors.MerchantNotFoundError());
        }

        const merchant = new MerchantImpl(item);
        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });

        if (brand.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }

        return merchant;
    }

    public async findAllByType(type: string | string []): Promise<MerchantImpl[]> {
        const query: WhereOptions<any> = {};
        const types = Array.isArray(type) ? type : type.split(",").map(t => t.trim());
        query.type = types.length > 1 ? { [Op.in]: types } : type;

        const items: MerchantDBInstance[] = await MerchantModel.findAll({ where: query });
        const merchants = items.map(item => new MerchantImpl(item));
        const activeMerchants: MerchantImpl[] = [];
        for (const merchant of merchants) {
            const brand = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
            if (!brand.isSuspended()) {
                activeMerchants.push(merchant);
            }
        }
        return activeMerchants;
    }

    public async findAllByTypeAndCode(type: string, code?: string): Promise<MerchantImpl[]> {
        if (code) {
            const merchant: MerchantImpl = await this.findOneByTypeAndCode(
                type,
                code
            );
            return [merchant];
        } else {
            return this.findAllByType(type);
        }
    }

    public async findOneByEntityId(brandId: number): Promise<MerchantImpl> {
        const item: MerchantDBInstance = await MerchantModel.findOne({
            where: {
                brandId
            },
            include: [ProxyModel]
        });

        if (!item) {
            throw new Errors.MerchantNotFoundError();
        }

        const merchant = new MerchantImpl(item);
        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: brandId });

        if (brand.isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }

        return merchant;
    }

    public async findAllByEntityKeys(keys: string[], proxyRequired: boolean = true): Promise<MerchantImpl[]> {
        const instances = await MerchantModel.findAll({
            include: [
                {
                    model: EntityModel,
                    where: {
                        key: {
                            [Op.in]: keys
                        }
                    }
                }, {
                    model: ProxyModel,
                    required: proxyRequired
                }
            ]
        });
        return instances.map(instance => new MerchantImpl(instance));
    }
}

const merchantInternalService = lazy<MerchantInternalService>(() => new MerchantInternalService());
export const getMerchantInternalService = () => merchantInternalService.get();

interface ExternalRoundsGroupedByRoundId {
    [prop: number]: WinBetHistoryResponseItem[];
}

class MerchantInternalService {

    public async getHistoryUrl(req: MerchantGameHistoryRequest): Promise<GameHistoryVisualisation> {
        const merchant = await getMerchantSearchService().findOneByTypeAndCode(req.merchantType, req.merchantCode);
        const entity = await EntityCache.findOne<BaseEntity>({ id: merchant.brandId });
        if (req.roundId === undefined || req.roundId === null || isNaN(+req.roundId)) {
            throw new Errors.ValidationError("roundId - invalid value");
        }

        const [round] = await HistoryService.findGameHistoryEntries(
            entity,
            parseFilter({ roundId: req.roundId, limit: 1, ts__lte: req.finishDate },
                roundHistoryQueryKeysForRoundIdQueries
            )
        );
        if (!round) {
            // Try to find external round for ITG instead
            const payload = this.buildExtHistoryPayload(entity, req);
            const [extRound] = await ExternalHistoryService.getWinBetHistory(payload);
            if (!extRound) {
                throw new Errors.GameHistoryDetailsNotFound();
            }
            const game = await getGame(extRound.gameCode);
            if (!game) {
                throw new Errors.GameNotFoundError(extRound.gameCode);
            }
            payload.licenseeId = game.features?.licenseeId || envConfig.itgLicenseeId;
            let historyDetails: AsyncReturnType<typeof ExternalHistoryService.getWinBetHistoryDetails>;
            try {
                historyDetails = await ExternalHistoryService.getWinBetHistoryDetails(payload);
            } catch (err) {
                if (err.code === 40) {
                    throw new Errors.ValidationError(err.message);
                }
                throw err;
            }
            if (historyDetails) {
                return { imageUrl: historyDetails.result };
            }
            throw new Errors.GameHistoryDetailsNotFound();
        }

        const brand = await EntityCache.findOne<BaseEntity>({ id: round.brandId });

        const options: GHVisualizationOptions = {
            language: req.language || (brand as ChildEntity).defaultLanguage,
            ttl: req.ttl,
            timezone: req.timezone,
            firstTs: round.firstTs && round.firstTs.toISOString(),
            ts: req.finishDate ? req.finishDate : round.ts && round.ts.toISOString(),
            gameCode: round.gameCode,
            currency: round.currency,
        };
        if (req.showRoundInfo) {
            options.showRoundInfo = req.showRoundInfo;
        }

        return await HistoryService.getGameHistoryDetailsImageUrl(brand, +req.roundId, options);
    }

    public async getGameHistoryDetailsImage(
        req: MerchantGameHistoryDetailsImageRequest): Promise<GameHistoryVisualisation> {
        const merchant = await getMerchantSearchService().findOneByTypeAndCode(req.merchantType, req.merchantCode);
        const entity: BaseEntity = await EntityCache.findOne<BaseEntity>({ id: merchant.brandId });
        const roundId = +req.roundId;
        const spinNumber = req.spinNumber || 0;
        const { currency, gameCode } = await HistoryService.getGameHistoryDetails(entity, roundId, spinNumber);
        return HistoryService.getGameHistoryDetailsImageUrl(entity, roundId, {
            language: req.language,
            timezone: req.timezone,
            spinNumber,
            gameCode: req.gameCode ? req.gameCode : gameCode,
            currency
        });
    }

    /**
     * This force finish method is to be used by internal api and was initially made for POP.
     * Logic behind this method is following: Operator that already closed a round on his side will call our external
     * API, for example pop api, that in turn will call this method in order to force finish round on our side
     * (and we are not expected to call their side to 'notify' them of successful round closure)
     */
    public async forceFinishRound(req: MerchantResolveRoundRequest,
                                  reverted: boolean = false): Promise<ForceFinishRoundResponse> {
        const merchant: MerchantImpl = await getMerchantSearchService()
            .findOneByTypeAndCode(req.merchantType, req.merchantCode);

        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
        if (req.roundId === undefined || req.roundId === null || isNaN(+req.roundId)) {
            throw new Errors.ValidationError("roundId - invalid value");
        }

        const round: UnfinishedRoundHistory = await getRoundHistoryServiceFactory()
            .getUnfinishedRoundsHistoryService().findOne(brand, {
                roundId: req.roundId,
                brandId: brand.id,
                playerCode: req.playerCode
            });

        if (!round) {
            // Try to find an external round instead
            const payload = this.buildExtHistoryPayload(brand, req);
            const [extHistoryItem] = await ExternalHistoryService.getWinBetHistory(payload);
            if (!extHistoryItem) {
                return Promise.reject(new Errors.GameHistoryDetailsNotFound());
            }
            const externalGame = await getGame(extHistoryItem.gameCode);
            if (!externalGame) {
                return Promise.reject(new Errors.GameNotFoundError(extHistoryItem.gameCode));
            }
            const forceFinishRequest = {
                roundId: extHistoryItem.roundId,
                features: externalGame.features
            } as any;
            if (extHistoryItem) {
                return ExternalForceFinishService.forceFinish(forceFinishRequest);
            }
            return Promise.reject(new Errors.GameHistoryDetailsNotFound());
        }

        if (round.status === "requireTransferOut") {
            return Promise.reject(new Errors.ValidationError("Its forbidden to finalize action games"));
        }

        const game = await GameService.findOne({
            code: round.gameCode
        });

        const isJPGame = game?.features?.jackpotTypes?.length;

        if (reverted) {
            if (isJPGame) {
                return Promise.reject(new Errors.OperationForbidden("It is forbidden to revert JP round"));
            }
            if (round.status === "broken") {
                return Promise.reject(
                    new Errors.OperationForbidden("It is forbidden to revert round with broken payment"));
            }
        } else if (isJPGame && round.status === "broken") {
            return Promise.reject(
                new Errors.OperationForbidden("It is forbidden to force-finish JP round with broken payment"));
        }

        // ensure that we have un-encoded value for brand id here as GS may return brandID encoded
        round.brandId = brand.id;
        // ensure roundId is not encoded
        round.roundId = decodeRoundId(round);

        const service = await UnfinishedRoundManagementServiceFactory.getUnfinishedRoundManagementService(
            brand,
            true,
            true);

        const response = await service.forceFinish(round, undefined, true, reverted);
        if (reverted) {
            await this.logTransaction(brand, round);
        }

        return response;
    }

    public async startFinalizeGame(req: MerchantGameFinalizeRequest): Promise<StartFinalizeResponse> {
        const merchant: MerchantImpl = await getMerchantSearchService()
            .findOneByTypeAndCode(req.merchantType, req.merchantCode);
        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
        const game: Game = await getGame(req.gameCode);

        return createFinalizeService(brand, game)
            .startFinalize(req.merchantSessionId, req.playerCode, req.gameCode, req.currency);
    }

    public async completeFinalizeGame(req: MerchantGameFinalizeRequest): Promise<void> {
        const merchant: MerchantImpl = await getMerchantSearchService()
            .findOneByTypeAndCode(req.merchantType, req.merchantCode);
        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
        const game: Game = await getGame(req.gameCode);
        return createFinalizeService(brand, game).completeFinalize(req.merchantSessionId, req.playerCode, req.currency);
    }

    public async killPlayerSession(reqData: MerchantPlayerKillSessionInternalRequest): Promise<boolean> {
        const merchant: MerchantImpl = await getMerchantSearchService()
            .findOneByTypeAndCode(reqData.merchantType, reqData.merchantCode);
        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });

        const service: PlayerSessionFacade = createPlayerSessionFacade();

        const params = {
            brandId: brand.id,
            playerCode: reqData.playerCode,
            reason: reqData.reason ? reqData.reason : `Player session kill requested by ${merchant.code} merchant`
        };
        return service.kill(params);
    }

    public async getGameRoundDetails(
        type: string,
        code: string,
        roundId: string,
        includeSpins: boolean = false,
        includePending: boolean = false
    ): Promise<RoundHistoryExtended> {

        const merchant = await getMerchantSearchService().findOneByTypeAndCode(type, code);
        const parentEntity = await EntityCache.findOne({ id: merchant.brandId });
        const [round] = await HistoryService.findGameHistoryEntries(parentEntity, {
            roundId,
            limit: 1
        });
        if (round) {
            const roundDetails = round as RoundHistoryExtended;
            if (includeSpins) {
                const childEntity = await EntityCache.findById(roundDetails.brandId);
                roundDetails.spins = await this.getSpinListByBrand(childEntity, roundId, includePending);
                if (includePending && !roundDetails.finished) {
                    const unfinishedRound = await this.getUnfinishedRoundByBrand(childEntity, {
                        playerCode: roundDetails.playerCode,
                        gameCode: roundDetails.gameCode,
                        ...(includeSpins ? { includeBrokenSpin: "true", withTrx: "true" } : {})
                    });
                    if (unfinishedRound) {
                        roundDetails.finished = unfinishedRound.finished;
                        if (Array.isArray(unfinishedRound.spins)) {
                            roundDetails.spins = [
                                ...roundDetails.spins,
                                ...unfinishedRound.spins
                            ].sort((a, b) => new Date(a.ts).getTime() - (new Date(b.ts)).getTime());
                        }
                    }
                }
            }
            return roundDetails;
        }

        if (includePending) {
            const spinDetails = await this.getSpinListByEntity(parentEntity, roundId, includePending);
            const unfinishedRound = await this.getUnfinishedRoundByEntity(parentEntity, {
                roundId,
                ...(includeSpins ? { includeBrokenSpin: "true", withTrx: "true" } : {})
            });
            if (unfinishedRound) {
                if (spinDetails.length) {
                    unfinishedRound.spins = [
                        ...(unfinishedRound.spins || []),
                        ...spinDetails
                    ].sort((a, b) => new Date(a.ts).getTime() - (new Date(b.ts)).getTime());
                }
                return unfinishedRound;
            }
            if (spinDetails.length) {
                const unfinishedSpins = spinDetails.filter(({ state }) => state !== "cancelled");
                return {
                    ...this.aggregateExternalRound(spinDetails, includeSpins),
                    firstTs: new Date(spinDetails[spinDetails.length - 1].ts),
                    ts: new Date(spinDetails[0].ts),
                    finished: unfinishedSpins.length > 0 && unfinishedSpins.some(({ endOfRound }) => endOfRound)
                };
            }
        }

        // Try to find the external round for ITG instead
        const payload = this.buildExtHistoryPayload(parentEntity, { roundId });
        const spins = await ExternalHistoryService.getWinBetHistory(payload);
        if (spins.length) {
            return this.aggregateExternalRound(spins, includeSpins);
        }

        return Promise.reject(new Errors.GameHistoryDetailsNotFound());
    }

    private aggregateExternalRounds(spins: WinBetHistoryResponseItem[], includeSpins?: boolean) {
        const externalRounds: RoundHistoryExtended[] = [];
        for (const roundSpins of Object.values(this.groupSpinsBy(spins, "roundId"))) {
            externalRounds.push(this.aggregateExternalRound(roundSpins, includeSpins));
        }
        return externalRounds;
    }

    private groupSpinsBy(spins: WinBetHistoryResponseItem[], property: string): ExternalRoundsGroupedByRoundId {
        return spins.reduce((accumulator: ExternalRoundsGroupedByRoundId, current: WinBetHistoryResponseItem) => {
            accumulator[current[property]] = [...accumulator[current[property]] || [], current];
            return accumulator;
        }, {});
    }

    private aggregateExternalRound(spins: WinBetHistoryResponseItem[], includeSpins?: boolean): RoundHistoryExtended {
        let totalBet = 0;
        let totalWin = 0;
        let totalEvents = 0;
        let finished = false;
        for (const spin of spins) {
            totalBet += spin.bet;
            totalWin += spin.win;
            totalEvents++;
            if (spin.finished) {
                finished = true;
            }
        }
        return {
            finished,
            revenue: totalBet - totalWin,
            currency: spins[0].currency,
            roundId: spins[0].roundId,
            brandId: spins[0].brandId,
            playerCode: spins[0].playerCode,
            gameCode: spins[0].gameCode,
            win: totalWin,
            bet: totalBet,
            balanceBefore: spins[spins.length - 1].balanceBefore,
            balanceAfter: spins[0].balanceAfter,
            totalEvents,
            firstTs: (spins[spins.length - 1] as any).insertedAt,
            ts: (spins[0] as any).insertedAt,
            isTest: false,
            smResult: undefined,
            smResultExtraData: undefined,
            spins: includeSpins ? spins : undefined
        } as any;
    }

    private async getSpinListByEntity(
        entity: BaseEntity,
        roundId: string,
        includePending = false
    ): Promise<GameHistorySpinExtended[]> {
        const brands = entity.isBrand() ? [entity] : getChildEntities(entity);
        for (const brand of brands) {
            const spins = await this.getSpinListByBrand(brand, roundId, includePending);
            if (spins.length) {
                return spins;
            }
        }
        return [];
    }

    private async getSpinListByBrand(
        entity: BaseEntity,
        roundId: string,
        includePending = false
    ): Promise<GameHistorySpinExtended[]> {
        const spins: GameHistorySpinExtended[] = await getSpinListByBrand(
            entity,
            { roundId, isPayment: "true" },
            { includeTrxId: true }
        );
        if (includePending) {
            const historyService = createBetWinHistoryService(sequelize);
            const pendingSpins = await historyService.getEvents([entity.id], +roundId);
            if (pendingSpins.length) {
                const { currencyFormatSettings } = await new EntitySettingsService(entity).get();
                for (const { isTest, ...pendingSpin } of pendingSpins) {
                    const spin: GameHistorySpinExtended = {
                        spinNumber: null,
                        device: null,
                        type: null,
                        endOfRound: null,
                        isPayment: true,
                        details: null,
                        gameId: null,
                        gameVersion: null,
                        test: isTest,
                        ...pendingSpin
                    };
                    if (currencyFormatSettings && spin.currency) {
                        const currencyFormatConfig = currencyFormatSettings[spin.currency];
                        if (currencyFormatConfig) {
                            spin.currencyFormatConfig = currencyFormatConfig;
                        }
                    }
                    spins.push(spin);
                }
            }

        }
        return spins.sort((a, b) => new Date(a.ts).getTime() - (new Date(b.ts)).getTime());
    }

    private async getUnfinishedRoundByEntity(
        entity: BaseEntity,
        query: UnfinishedRoundsQuery
    ): Promise<RoundHistoryExtended | null> {
        const brands = entity.isBrand() ? [entity] : getChildEntities(entity);
        for (const brand of brands) {
            const round = await this.getUnfinishedRoundByBrand(brand, query);
            if (round) {
                return round;
            }
        }
        return null;
    }

    private async getUnfinishedRoundByBrand(
        entity: BaseEntity,
        query: UnfinishedRoundsQuery
    ): Promise<RoundHistoryExtended | null> {
        const service = getRoundHistoryServiceFactory().getUnfinishedRoundsHistoryService();
        const rounds = await service.getRounds(entity, query);
        if (rounds.length) {
            const { pendingSpin, finished, ...data } = rounds.shift();
            const endOfRound = pendingSpin && pendingSpin.endOfRound;
            const round: RoundHistoryExtended = {
                ...data,
                finished: endOfRound === true ? true : finished,
                smResult: undefined,
                smResultExtraData: undefined,
            };
            if (pendingSpin) {
                const { currencyFormatSettings } = await new EntitySettingsService(entity).get();
                const firstTs = data.firstTs;
                const ts = typeof firstTs === "string" ? firstTs : firstTs && firstTs.toISOString();
                const spin: GameHistorySpinExtended = {
                    roundId: decodeRoundId(data),
                    spinNumber: pendingSpin.spinNumber,
                    device: data.device || null,
                    type: pendingSpin.type || null,
                    currency: data.currency,
                    bet: pendingSpin.bet,
                    win: pendingSpin.win,
                    balanceBefore: data.balanceBefore,
                    balanceAfter: data.balanceAfter,
                    endOfRound: pendingSpin.endOfRound || false,
                    ts,
                    test: pendingSpin.test,
                    isPayment: true,
                    details: null,
                    gameId: data.gameId || null,
                    gameVersion: null,
                    trxId: pendingSpin.walletTransactionId
                };
                if (currencyFormatSettings && spin.currency) {
                    const currencyFormatConfig = currencyFormatSettings[spin.currency];
                    if (currencyFormatConfig) {
                        spin.currencyFormatConfig = currencyFormatConfig;
                    }
                }
                round.spins = [spin];
            }
            return round;
        }
        return null;
    }

    public async getGameHistory(type: string,
                                code: string,
                                request: { query: WhereOptions<any> } = { query: {} },
                                includeSpins: boolean = false,
                                includeSmResultExtraData: boolean = false)
        : Promise<RoundHistoryExtended[]> {

        const merchant = await getMerchantSearchService().findOneByTypeAndCode(type, code);
        const parentEntity = await EntityCache.findOne({ id: merchant.brandId });

        const filterKeys = request.query["roundId"] || request.query["roundId__in"] ?
                           roundHistoryQueryKeysForRoundIdQueries : HistoryService.queryParamsKeys;

        const rounds = await HistoryService.findGameHistoryEntries(parentEntity,
            parseFilter(request.query, filterKeys));

        if (!rounds.length) {
            // Try to find external rounds for ITG instead
            const payload = this.buildExtHistoryPayload(parentEntity, request.query);
            const spins = await ExternalHistoryService.getWinBetHistory(payload);
            if (spins.length) {
                return this.aggregateExternalRounds(spins, includeSpins);
            }
            return Promise.reject(new Errors.GameHistoryDetailsNotFound());
        }

        const extendedRounds = rounds as RoundHistoryExtended[];

        if (includeSpins) {
            await Promise.all(extendedRounds.map(async (round) => {
                const childEntity = await EntityCache.findById(round.brandId);
                round.spins = await getSpinListByBrand(childEntity,
                    { roundId: decodeRoundId(round), isPayment: "true" },
                    { includeTrxId: true });
            }));
        }

        if (includeSmResultExtraData) {
            await Promise.all(extendedRounds.map(async (round) => {
                const childEntity = await EntityCache.findById(round.brandId);
                const { smResult, smResultExtraData } = await getSmResultWithExtraData(childEntity,
                    { roundId: decodeRoundId(round) }
                );
                round.smResult = smResult;
                round.smResultExtraData = smResultExtraData;
            }));
        }

        return extendedRounds;
    }

    private buildExtHistoryPayload(parentEntity: BaseEntity | number, reqQuery: any): any {
        const payload = {
            gameProviderCode: reqQuery.gameProviderCode || "ITG" // default to ITG for backwards compatibility for now
        } as any;

        if (typeof parentEntity !== "number") {
            if (!parentEntity.isBrand()) {
                payload.brandId__in = getChildIds(parentEntity).join(",");
            } else {
                payload.brandId = parentEntity.id;
            }
        } else {
            payload.brandId = parentEntity;
        }

        if (reqQuery["roundId"]) {
            payload.roundId = reqQuery["roundId"];
        }
        if (reqQuery["roundId__in"]) {
            payload.roundId__in = reqQuery["roundId__in"];
        }
        return payload;
    }

    private async logTransaction(brand: BrandEntity, round: RoundHistory) {
        const walletKey = PlayerWalletImpl.toWalletKey(brand.id, round.playerCode, round.currency);
        const walletCurrency = Currencies.get(round.currency);

        const changes: IChangeInfo[] = [];

        if (round.win) {
            changes.push({
                amount: -walletCurrency.toMinorUnits(round.win),
                trxType: WALLET_TRX_TYPE.BET,
                walletKey,
                account: PLAYER.PLAYER_MAIN_ACCOUNT,
                property: PLAYER.PLAYER_BALANCE,
            });
        }

        if (round.bet) {
            changes.push({
                amount: walletCurrency.toMinorUnits(round.bet),
                trxType: WALLET_TRX_TYPE.WIN,
                walletKey,
                account: PLAYER.PLAYER_MAIN_ACCOUNT,
                property: PLAYER.PLAYER_BALANCE,
            });
        }

        const trxId = await WalletFacade.generateTrxId();
        const externalTransaction: IExternalTransaction = {
            trxId,
            operation: {
                operationId: OPERATION_ID.WIN,
                operationName: WALLET_OPERATION_NAME.TO_EXTERNAL,
                gameId: round.toString(),
                params: {
                    gameCode: round.gameCode,
                    isTest: round.isTest
                }
            },
            changes
        };

        return WalletFacade.logExternalTransaction(externalTransaction);
    }

}

const merchantService = lazy<MerchantService>(() => new MerchantService());
export const getMerchantService = () => merchantService.get();

export interface MerchantGameURLConfig {
    ip?: string;
    ignoreWebSiteWhitelistedCheck?: boolean;
    player?: PlayerShortLoginTokenData;
    isLobby?: boolean;
    lobbySessionId?: string;
}

class MerchantService {

    public async getGameUrl(
        req: MerchantGameInitRequest,
        initConfig: MerchantGameURLConfig,
        merchantEntity?: Merchant
    ): Promise<PlayerGameURLInfo> {

        let merchant: Merchant = merchantEntity;
        if (!merchant) {
            merchant = await MerchantCache.findByTypeAndCode(req.merchantType, req.merchantCode);
        }
        const brand: BrandEntity = await this.getBrand(merchant);

        req.ip = req.ip || initConfig.ip;
        req.playmode = req.playmode || PlayMode.REAL;
        validatePlaymode(brand, req.playmode, merchant);

        const entitySettings = await getEntitySettings(brand.path);
        const entityGame = await this.getEntityGame(merchant, req.gameCode, initConfig.player?.code, entitySettings.cacheEntityGames);

        return getGameURLInfo({
            entityGame,
            brand,
            entitySettings,
            disableLauncher: false,
            ignoreWebSiteWhitelistedCheck: initConfig.ignoreWebSiteWhitelistedCheck,
            merchant,
            player: initConfig.player,
            isLobby: initConfig.isLobby,
            request: {
                ...req,
                lobbySessionId: initConfig.lobbySessionId,
                ...(req.isReturnJackpotIdsEnabled && getJackpotIds(entityGame))
            },
        });
    }

    public async getLobbyUrl(merchantCode: string, merchantType: string, ticket: string,
                             id?: number, language?: string): Promise<string> {
        const { brandId } = await MerchantCache.findByTypeAndCode(merchantType, merchantCode);
        const lobby = await LobbyModel.findOne({
            where: {
                brandId,
                ...(id ? { id } : { isDefault: true })
            }
        });
        if (!lobby) {
            return Promise.reject(new Errors.LobbyNotFound());
        }
        const lobbyId = encodeId(+lobby.get("id"));
        const url = `https://${lobbyId}-${config.lobbies.domainTemplate}/#/ticket/${(ticket)}`;
        return language ? `${url}?language=${language}` : url;
    }

    public async loginPlayer(baseEntity: BaseEntity, body: any, ip?: string): Promise<any> {
        const merchant = await getMerchantCRUDService().findOne(baseEntity);
        const { tokenData, sessionId } = await (await merchant.getAdapter(true)).loginTerminalPlayer(merchant, body);

        let entity = baseEntity;
        let entitySettings = await getEntitySettings(baseEntity.path);
        // For case unified-lobby & brand-as-merchants try to get player entity
        if (entitySettings.isPlayerCodeUniqueInSubtree) {
            const childEntity = baseEntity as ChildEntity;
            const parentEntity = (childEntity.getParent && childEntity.getParent()) || baseEntity;
            const childIds = [parentEntity.id, ...getChildIds(parentEntity)];
            const playerData = await PlayerModel.findOne({
                where: {
                    brandId: { [Op.in]: childIds },
                    code: tokenData.playerCode,
                }
            });
            if (playerData) {
                const brandEntity = await EntityCache.findById(playerData.get("brandId"));
                if (brandEntity) {
                    entity = brandEntity;
                    entitySettings = await getEntitySettings(entity.path);
                }
            }
        }

        let countrySource: CountrySource;
        try {
            const playerInfo = await getPlayerInfoService().getPlayerInfo(tokenData.playerCode, entity.id);
            countrySource = await getMerchantCountrySource({
                entitySettings,
                merchantParams: merchant.params,
                playerInfo,
                countryCode: tokenData.country,
                ip
            });

            await validatePlayerCountryRestrictions(entity, entitySettings, tokenData.currency, countrySource);
        } catch (err) {
            await sendPlayerBlockingAnalytics(err, countrySource, {
                initiator: "player-login",
                playerCode: tokenData.playerCode,
                currencyCode: tokenData.currency,
                ip,
                operatorCountryCode: tokenData.country,
                brandId: entity.id
            });

            return Promise.reject(err);
        }

        const gameGroupInfo = await MerchantService.getGameGroupInfo(entity, tokenData.gameGroup);

        const token: string = await generatePlayerLoginToken({
            ...tokenData,
            ...(gameGroupInfo || {}),
            brandId: entity.id,
            merchantCode: merchant.code,
            merchantType: merchant.type,
            test: typeof tokenData.test === "boolean" ? tokenData.test : entity.isTest,
            isExternalTerminal: true,
            sessionId
        });

        return {
            code: tokenData.playerCode,
            token,
            customerSessionId: sessionId,
        };
    }

    private static async getGameGroupInfo(entity: BaseEntity,
                                          gameGroup: string): Promise<{ gameGroup: string, gameGroupId: number }> {
        const settings = await getEntitySettings(entity.path);

        let gameGroupDB;
        if (gameGroup) {
            gameGroupDB = await getGameGroupService().findOne(entity, {
                name: gameGroup
            }, false, settings.gameGroupsInheritance);
        }

        if (!gameGroupDB) {
            gameGroupDB = await getGameGroupService().findOneDefault(entity, settings);
        }

        if (gameGroupDB) {
            const defaultGameGroup = gameGroupDB.toJSON();

            return {
                gameGroup: defaultGameGroup.name,
                gameGroupId: defaultGameGroup.id
            };
        }
    }

    public async getPlayerWithBalance(tokenData: PlayerLoginTokenData): Promise<PlayerInfoWithBalance> {

        const merchant = await getMerchantSearchService().findOneByEntityId(+tokenData.brandId);
        const balances = await (await merchant.getAdapter(true)).getBalances(merchant.toInfo(), tokenData as any);
        const rewards = await getRewards(merchant.brandId, tokenData.playerCode, tokenData.currency);

        const {
            playerCode,
            isMerchantPlayer,
            restrictedIpCountries,
            ...additionalInfo
        } = await getPlayerInfoService().getPlayerInfo(tokenData.playerCode, merchant.brandId);

        return {
            code: tokenData.playerCode,
            isTest: tokenData.test,
            brandId: tokenData.brandId,
            language: tokenData.language,
            country: tokenData.country,
            gameGroup: tokenData.gameGroup,
            currency: tokenData.currency,
            status: "normal",
            balances,
            rewards,
            ...additionalInfo
        };
    }

    public async getPage(entity: BaseEntity,
                         getPageRequest: MerchantPageRequest): Promise<MerchantPageResponse> {
        const merchant = await getMerchantSearchService().findOneByEntityId(entity.id);

        return (await merchant.getAdapter()).getPage(merchant, getPageRequest);
    }

    private async getBrand(merchant: Merchant): Promise<BrandEntity> {
        const brand = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
        if (brand.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }
        return brand;
    }

    public async getEntityGame(merchant: Merchant, gameCode: string, playerCode?: string, useCache?: boolean): Promise<EntityGame> {
        const brand: BrandEntity = await this.getBrand(merchant);
        try {
            const throwErrorForHidden = true;
            return getEntityGame(brand, gameCode, throwErrorForHidden, useCache);
        } catch (err) {
            log.error(err.message, JSON.stringify({
                gameCode,
                playerCode,
                entityId: brand.id,
                merchantId: merchant.brandId,
                errorCode: err.code
            }));
            throw err;
        }
    }

}

function decodeRoundId(round: RoundHistory) {
    return isNaN(round.roundId as any) ? decodeId(round.roundId).toString() : round.roundId;
}

function getJackpotIds(entityGame: EntityGame) {
    if (entityGame?.settings?.jackpotId) {
        const ids = Object.values(entityGame?.settings?.jackpotId).toString();
        if (ids.length) {
            return { jackpotIds: ids };
        }
    }
    return { jackpotIds: null };
}
