import * as Errors from "../errors";
import { findPlayerLimits } from "./limits";
import * as GameService from "../services/game";
import { findAllGameRelatedToRush, upsertRecentlyGame } from "./game";
import { BaseEntity, EntityStatus } from "../entities/entity";
import MigrationService from "./migrationService";
import {
    DetailedStartGameResponse,
    GamesListRequest,
    GamesListResponse,
    PlayerGameInfoRequest,
    StartGameSettings
} from "../entities/gameprovider";
import * as TokenUtils from "../utils/token";
import { Player, PlayerInfo } from "../entities/player";
import { OPERATION_ID, WalletErrors, WalletFacade } from "@skywind-group/sw-management-wallet";
import { BrandEntity } from "../entities/brand";
import { Merchant, MerchantParams } from "../entities/merchant";
import EntityCache from "../cache/entity";
import * as MerchantCache from "../cache/merchant";
import {
    BrandClientFeatures,
    DEFAULT_GAME_JACKPOT_SETTINGS,
    DEFAULT_MARKETING_JACKPOT_SETTINGS,
    DEFAULT_PHANTOM_JACKPOT_SETTINGS,
    EntityGame,
    Game,
    GameClientFeatures,
    GameFeatures,
    JackpotSettings,
    LiveTable,
    PlayerGameURLInfo
} from "../entities/game";
import { Limits, SlotGameLimits } from "../entities/gamegroup";
import { ENTITY_GAME_STATUS, GAME_TYPES, roundNumber } from "../utils/common";
import { validateEntityBalance } from "./entity";
import { DECLINED, GetTransactionInfoResponse, TrxStatus } from "../entities/payment";
import { ClientFeatures, EntitySettings } from "../entities/settings";
import EntitySettingsService, { getEntitySettings } from "./settings";
import { getPlayerResponsibleGamingService, PlayerResponsibleGamingImpl } from "./playerResponsibleGaming";
import PlayerSessionPromotion from "./promotions/playerSessionPromotion";
import * as UrlManager from "./urlManager";
import { JurisdictionCodes } from "../entities/jurisdiction";
import { token } from "@skywind-group/sw-utils";
import { getEntityDynamicDomainService, validateEntityEnvironment } from "./entityDynamicDomainService";
import config from "../config";
import {
    AdditionalInfo,
    BrandFinalizationType,
    GameTokenData,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantInfo,
    MerchantStartGameTokenData,
    PlayMode,
    StartGameTokenData,
} from "@skywind-group/sw-wallet-adapter-core";
import { getGameGroupService } from "./gamegroup";
import { validateGameCountryRestrictions } from "../utils/validateCountriesRestrictions";
import { MERCHANT_POP_TYPES, MerchantInfoHelper, POPAuthTokenData } from "@skywind-group/sw-management-adapters";
import { getPhantomService, simplisticBooleanParam } from "../phantom/service";
import { GetPhantomJackpotsRequest, PhantomPayload } from "../phantom/protocol";
import { getEntityPlayerFinanceService } from "./payment";
import logger from "../utils/logger";
import { getSearchPlayerSegmentService } from "./gameLimits/segment";
import { getMerchantService } from "./merchant";
import * as BlockedPlayersService from "./blockedPlayer";
import getMerchantTestPlayerService from "./merchantTestPlayer";
import { checkBrand } from "./brand";
import { BONUS_COIN_CURRENCY, PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { getNewLimitsFacade } from "./gameLimits/limitsFacade";
import * as merchantTestPlayersCache from "../cache/testPlayers";
import {
    AdditionalSessionInfo,
    FunStartGameToken,
    GameSessionInfo,
    StartFunGameRequest,
    StartGameRequest,
    StartGameResult
} from "@skywind-group/sw-management-gameprovider";
import { defaultOperatorDetailsRepository } from "./gameauth/defaultOperatorInfoRepository";
import { defaultDeferredPaymentFacade } from "./deferredPayments";
import { rtpService } from "./gameRTPHistory";
import { createPlayerSessionFacade } from "./player/playerSessionFacade";
import { getMerchantPlayerGameGroupService } from "./merchantPlayerGameGroup";
import { GameGroupInfo } from "../entities/merchantPlayerGameGroup";
import { getPlayerInfoService } from "./playerInfo";
import { checkSiteIsAuthorized, getAvailableSiteService } from "./availableSites";
import { getBrandPlayerService } from "./brandPlayer";
import { getGameClientVersionService } from "./gameVersionService";
import { PlayerPromotionService } from "./promotions/playerPromotionService";
import { DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";
import { getIpLocationService } from "../utils/iplocation";
import { CountrySource, getCountrySource } from "../utils/countrySource";
import { sendPlayerBlockingAnalytics } from "./playerBlockingAnalyticsService";
import { EntityHelper } from "./gameUrl/entityHelper";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { isLiveRush } from "./games/liveGame";
import { SOURCE } from "../entities/merchantTestPlayer";
import { GameVersionInfo } from "../entities/gameClientVersion";
import * as EntityJurisdictionCache from "../cache/entityJurisdiction";
import * as EntityGameCache from "../cache/entityGame";
import { getEntityStaticDomainService } from "./entityStaticDomainService";
import { StaticDomainType } from "../entities/domain";
import { getJurisdictionService } from "./jurisdiction";

const log = logger("playService");

const SWISS_MERCHANT_REGULATION = "swiss";

export function isTransferEnabled(entityGame: EntityGame, merchant: Merchant, playmode: string) {
    return playmode !== PlayMode.BNS &&
        merchant.params.supportTransfer && entityGame.game.features.transferEnabled;
}

export async function getMerchantGameSettings(brand: BrandEntity,
    entityGame: EntityGame,
    entitySettings: EntitySettings,
    merchant: Merchant,
    playmode: string,
    limits: Limits,
    currency: string): Promise<StartGameSettings> {

    const settings = await buildGameSettings(brand, entityGame, entitySettings, playmode, limits, currency);

    settings.transferEnabled = isTransferEnabled(entityGame, merchant, playmode);
    if (merchant.params.walletPerGame) {
        settings.walletPerGame = merchant.params.walletPerGame;
    }

    if (merchant.params.enforceSingleTransferInAndOutPerRound) {
        settings.enforceSingleTransferInAndOutPerRound = merchant.params.enforceSingleTransferInAndOutPerRound;
    }

    if (settings.transferEnabled && merchant.params.keepAliveSec) {
        settings.keepAliveSec = merchant.params.keepAliveSec;
    }

    if (entitySettings.disableCloseRoundForBonusMode) {
        settings.disableCloseRoundForBonusMode = entitySettings.disableCloseRoundForBonusMode;
    }

    // for backward compatibility -
    // if gamble is set in merchant params and is not set in entity settings, then get the value from params
    if (("gamble" in merchant.params) && !("gamble" in settings)) {
        settings.gamble = merchant.params.gamble;
    }

    return settings;
}

function mergeLiveTables(tables: LiveTable[], games: any[]) {
    for (const table of tables) {
        const liveGame = games.find((game) => game.code === table.gameCode);
        if (liveGame) {
            if (!table.tableName && liveGame.title) {
                table.tableName = liveGame.title;
            }
            const providerSettingsByGameCode = liveGame?.features?.live?.providerSettings;
            if (providerSettingsByGameCode &&
                (!table.providerSettings || !Object.keys(table.providerSettings).length)) {
                table.providerSettings = providerSettingsByGameCode;
            }
        }
        table.isAddedToEntity = Boolean(liveGame?.isAddedToEntity);
    }
}

export async function buildGameSettings(brand: BrandEntity,
    entityGame: EntityGame,
    entitySettings: EntitySettings,
    playmode: string,
    limits: Limits,
    currency: string): Promise<StartGameSettings> {

    const settings: StartGameSettings = { ...entityGame.settings };
    const game = entityGame.game;

    if (game.features.isGRCGame) {
        settings.isGRCGame = game.features.isGRCGame;
    }

    if ("highestPrizeProbability" in game.features) {
        settings.highestPrizeProbability = game.features.highestPrizeProbability;
    }

    if ("gamble" in entitySettings) {
        settings.gamble = entitySettings.gamble;
    }

    if (game.type === GAME_TYPES.live) {
        if (isLiveRush(game.features.live)) {
            const liveTables = game.features.live.tables;
            const gameCodes = liveTables.map(table => table.gameCode);
            const games = await findAllGameRelatedToRush(brand, gameCodes);
            mergeLiveTables(liveTables, games);
        }
        Object.assign(settings, game.features.live, { title: entityGame.title || game.title });

        if ("social" in entitySettings) {
            settings.social = entitySettings.social;
            // it is needed for backward compatibility until all GMs will be updated
            settings.isSocialCasino = entitySettings.social;
        }
        if ("useSocialCasinoOperator" in entitySettings) {
            settings.useSocialCasinoOperator = entitySettings.useSocialCasinoOperator;
        }
    }

    const gameFeatures: GameFeatures = game.features;
    const rtpConfiguratorSettings = settings.rtpConfigurator;
    const rtpDeductionSupportedForPlayMode = rtpService.get()
        .rtpDeductionSupportedForPlayMode(playmode as PlayMode, gameFeatures, entitySettings, rtpConfiguratorSettings);
    if (rtpDeductionSupportedForPlayMode) {
        settings.rtpConfigurator = {
            rtp: rtpService.get().rtpTheoretical(gameFeatures, rtpConfiguratorSettings),
            rtpDeduction: rtpService.get().rtpDeduction(rtpConfiguratorSettings)
        };
    } else {
        settings.rtpConfigurator = undefined;
    }

    if (entitySettings.autoPlaySettings) {
        settings.autoSpinsOptions = entitySettings.autoPlaySettings;
    }

    if ([GAME_TYPES.table, GAME_TYPES.live].includes(game.type)) {
        settings.possibleLimits = limits;
    }

    if (entitySettings.autoCreateTestJackpot) {
        settings.autoCreateTestJackpot = entitySettings.autoCreateTestJackpot;
    }

    if (gameFeatures.validateRequestsExtensionEnabled) {
        settings.validateRequestsExtensionEnabled = gameFeatures.validateRequestsExtensionEnabled;
    }

    if (gameFeatures.zeroBetCheckEnabled) {
        settings.zeroBetCheckEnabled = gameFeatures.zeroBetCheckEnabled;
    }
    if (game.type === GAME_TYPES.external) {
        settings.brandFinalizationType = entityGame.settings?.finalizationSupport || entitySettings.finalizationSupport;
    }
    if (gameFeatures.instantJpEnabled) {
        settings.instantJpEnabled = gameFeatures.instantJpEnabled;
    }

    if (entitySettings.roundDetailsReportEnabled) {
        settings.roundDetailsReportEnabled = entitySettings.roundDetailsReportEnabled;
    }

    if (entitySettings.fixedExtraData) {
        settings.fixedExtraData = entitySettings.fixedExtraData;
    }

    if (entitySettings.smResultEnabled) {
        settings.smResultEnabled = entitySettings.smResultEnabled;
    }

    if (currency && entitySettings.currencyFormatSettings) {
        const currencyFormatConfig = entitySettings.currencyFormatSettings[currency];
        if (currencyFormatConfig) {
            const { hideCurrencyInFunMode, ...config } = currencyFormatConfig;
            settings.currencyFormatConfig = config;
            if (playmode === PlayMode.FUN && hideCurrencyInFunMode) {
                settings.currencyFormatConfig.showCurrency = false;
            }
        }
    }

    return settings;
}

export async function getBrand(brandId: number, ignoreValidations?: boolean): Promise<BrandEntity> {
    const entity: BaseEntity = await EntityCache.findById(brandId);
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }
    if (!ignoreValidations) {
        return checkBrand(entity);
    }
    return entity as BrandEntity;
}

async function authenticateGame(brand: BrandEntity,
    startGameTokenData: StartGameTokenData,
    settings: EntitySettings,
    request: StartGameRequest): Promise<StartGameResult<DetailedStartGameResponse>> {

    const player: Player = await getBrandPlayerService()
        .findOneWithGameGroup(brand, { code: startGameTokenData.playerCode });
    if (player.isSuspended()) {
        throw Errors.PlayerIsSuspended.buildWithDefaultExtraData();
    }

    const entityGame: EntityGame = await GameService.findOneEntityGame(brand, startGameTokenData.gameCode);
    const isTestPlayer = startGameTokenData.test;
    if ((entityGame.status === ENTITY_GAME_STATUS.TEST) || (brand.status === EntityStatus.TEST)) {
        if (!isTestPlayer) {
            throw new Errors.MerchantTestPlayerError("only test player can play in tests game");
        }
    }

    // In case of brand-merchant login, need to skip check for player session
    if (startGameTokenData.lobbySessionId && !startGameTokenData.isExternalLogin) {
        await createPlayerSessionFacade()
            .find({
                brandId: brand.id,
                playerCode: startGameTokenData.playerCode,
                sessionId: startGameTokenData.lobbySessionId
            });
    }

    let limits: Limits;
    const currency: string = startGameTokenData.playmode === PlayMode.BNS
        ? BONUS_COIN_CURRENCY
        : startGameTokenData.currency;

    const newLimitsDisabled = entityGame?.settings?.newLimitsDisabled;

    const shouldExternalLimitsBeUsed = entityGame.game && entityGame.game.type === "external" &&
        settings.useGameProviderLimits;

    if (shouldExternalLimitsBeUsed) {
        limits = {};
    } else {
        if (settings.newLimitsEnabled && !newLimitsDisabled) {
            const gameGroupId = await getGameGroupId(brand, player.gamegroupName, settings);

            limits = await getNewLimitsFacade(brand).buildGameLaunch(
                entityGame.game,
                currency,
                gameGroupId,
                undefined,
                undefined,
                undefined,
                false,
                startGameTokenData.dynamicMaxTotalBetLimit
            );
        } else {
            [limits] = await findPlayerLimits(
                brand,
                entityGame,
                currency,
                player.gamegroupName,
                settings,
                false,
                startGameTokenData.dynamicMaxTotalBetLimit,
                startGameTokenData.dynamicDefaultTotalBet,
                startGameTokenData.dynamicMinTotalBetLimit
            );
        }
    }

    if (brand.migrationStatus) {
        await MigrationService.forceMigratePlayer(brand, startGameTokenData.playerCode);
    }

    const domain = await getEntityDynamicDomainService().get(brand);
    const gameTokenData: GameTokenData = {
        gameCode: startGameTokenData.gameCode,
        brandId: startGameTokenData.brandId,
        playerCode: startGameTokenData.playerCode,
        currency: startGameTokenData.currency,
        test: isTestPlayer,
        envId: domain ? domain.environment : undefined,
        playmode: startGameTokenData.playmode
    };
    decorateTokenDataWithGameSpecificData(gameTokenData, entityGame);
    decorateTokenDataWithRcSpecificData(gameTokenData, startGameTokenData);
    const gameToken = await TokenUtils.generateGameToken(gameTokenData);

    const jurisdiction = await EntityJurisdictionCache.findOne(brand);

    const playerInfo = await player.toInfo();
    const gameSettings = await buildGameSettings(
        brand,
        entityGame,
        settings,
        startGameTokenData.playmode,
        limits,
        currency
    );
    const sessionInfo: GameSessionInfo = await createPlayerSession(brand, startGameTokenData, settings, entityGame,
        {
            sharedPromoEnabled: settings.sharedPromoEnabled,
            rtpDeduction: gameSettings.rtpConfigurator?.rtpDeduction
        });

    const response: DetailedStartGameResponse = {
        gameToken: gameToken,
        player: {
            ...playerInfo,
            nicknameChangeAttemptsLeft: getNicknameChangeAttemptsLeft(playerInfo as any)
        },
        limits: limits,
        useGameProviderLimits: shouldExternalLimitsBeUsed ? true : undefined,
        settings: gameSettings,
        jrsdSettings: jurisdiction?.settings,
        jurisdictionCode: jurisdiction?.code,
        region: config.region,
        renderType: entityGame.game.historyRenderType,
    };

    await decorateStartGameDataWithPhantomPayload({
        startGameResponse: response,
        playerCode: player.code,
        game: entityGame.game,
        brand,
        entitySettings: settings,
        currency: startGameTokenData.currency,
        isTest: startGameTokenData.test,
        deviceData: request.deviceData,
        disablePlayerPhantomFeatures: startGameTokenData.disablePlayerPhantomFeatures
    });

    let deferredPayments = [];
    if (settings.bonusPaymentMethod === DeferredPaymentMethod.CREDIT) {
        deferredPayments = await defaultDeferredPaymentFacade.get().load(brand.id, startGameTokenData.playerCode);
    }

    return {
        response,
        deferredPayments,
        sessionInfo,
        operatorInfo: await defaultOperatorDetailsRepository.get().findById(brand.id)
    };
}

export async function createPlayerSession(brand: BrandEntity,
    startGameTokenData: GameTokenData,
    settings: EntitySettings,
    entityGame: EntityGame,
    options: AdditionalSessionInfo = {}): Promise<GameSessionInfo> {
    const isGRCGame: boolean = entityGame.game?.features?.isGRCGame;

    const sessionInfo: GameSessionInfo = await createPlayerSessionInfo(brand, startGameTokenData,
        { ...options, isGRCGame });

    const mustStoreExternalBetWinHistory: boolean =
        entityGame.game.gameProvider && entityGame.game.gameProvider.mustStoreExtHistory;
    if (mustStoreExternalBetWinHistory) {
        sessionInfo.mustStoreExternalBetWinHistory = true;
        sessionInfo.isNewExternalBetWinHistory = settings.newExternalBetWinHistoryEnabled;
        sessionInfo.gameProviderCode = entityGame.game.gameProvider.code;
        sessionInfo.offlineWin = entityGame.game.features.offlineWin;
        sessionInfo.operatorFinalizationSupport = settings.finalizationSupport;
        sessionInfo.operatorFinalizeOnOfflineWin = settings.finalizeOnOfflineWin;
    }

    return sessionInfo;
}

async function createPlayerSessionInfo(brand: BrandEntity,
    tokenData: GameTokenData,
    info?: AdditionalSessionInfo): Promise<GameSessionInfo> {
    if (info?.gameFreeBets?.isIndividualBonus) {
        return {
            promo: {
                freeBet: [info.gameFreeBets]
            },
            ...info
        };
    }

    const gamePromos = await PlayerSessionPromotion.getGamePromos(
        brand, tokenData.playerCode, tokenData.currency, tokenData.gameCode, info?.sharedPromoEnabled, info?.playerBonusesEnabled);
    if (tokenData.playmode === PlayMode.BNS && (!gamePromos || !gamePromos.bonusCoin)) {
        return Promise.reject(new Errors.BonusCoinsNotAvailableError());
    }
    return {
        promo: gamePromos,
        ...info
    };
}

async function authenticateFunGame(entity: BrandEntity,
    startGameTokenData: StartGameTokenData & { jCode?: string },
    player: PlayerInfo,
    entitySettings: EntitySettings,
    entityGame: EntityGame,
    deviceId?: string): Promise<DetailedStartGameResponse> {
    let limits;

    const newLimitsDisabled = entityGame.settings?.newLimitsDisabled;
    const shouldExternalLimitsBeUsed = entityGame.game && entityGame.game.type === "external" &&
        entitySettings.useGameProviderLimits;

    if (shouldExternalLimitsBeUsed) {
        limits = {};
    } else {
        if (entitySettings.newLimitsEnabled && !newLimitsDisabled) {
            limits = await getNewLimitsFacade(entity).buildGameLaunch(
                entityGame.game,
                startGameTokenData.currency,
                undefined,
                undefined,
                undefined,
                undefined,
                false,
                startGameTokenData.dynamicMaxTotalBetLimit
            );
        } else {
            [limits] = await findPlayerLimits(
                entity,
                entityGame,
                startGameTokenData.currency,
                (startGameTokenData as any).gameGroup,
                entitySettings,
                false,
                startGameTokenData.dynamicMaxTotalBetLimit,
                startGameTokenData.dynamicDefaultTotalBet,
                startGameTokenData.dynamicMinTotalBetLimit
            );
        }
    }

    const domain = await getEntityDynamicDomainService().get(entity);
    const playmode = PlayMode.FUN;

    const gameToken = await TokenUtils.generateGameToken({
        gameCode: startGameTokenData.gameCode,
        brandId: startGameTokenData.brandId,
        playerCode: startGameTokenData.playerCode,
        currency: startGameTokenData.currency,
        test: true,
        envId: domain ? domain.environment : undefined,
        playmode: playmode,
        defaultBalance: entitySettings.defaultFunGameBalance,
        deviceId
    });

    const gameSettings = await buildGameSettings(
        entity,
        entityGame,
        entitySettings,
        playmode,
        limits,
        startGameTokenData.currency
    );

    const result: DetailedStartGameResponse = {
        gameToken: gameToken,
        limits: limits,
        settings: gameSettings,
        gameMode: playmode,
        region: config.region,
        player,
    };

    if (entity.isBrand()) {
        const brand = entity as BrandEntity;
        const jurisdiction = await EntityJurisdictionCache.findOne(brand);
        result.jrsdSettings = jurisdiction?.settings;
        result.jurisdictionCode = jurisdiction?.code;
    }

    // In case If start game token doesn't exist and jCode is provided
    // Wrapper hardcoded brandID = 1
    if (startGameTokenData.jCode) {
        const jurisdiction = await getJurisdictionService().findOne(startGameTokenData.jCode);
        if (jurisdiction) {
            result.jrsdSettings = jurisdiction?.settings;
            result.jurisdictionCode = jurisdiction?.code;
        }
    }

    return result;
}

async function authenticateMerchantGame(brand: BrandEntity,
    startGameTokenData: StartGameTokenData,
    settings: EntitySettings,
    request: StartGameRequest)
    : Promise<StartGameResult<DetailedStartGameResponse>> {
    const merchantStartGameTokenData = startGameTokenData as MerchantStartGameTokenData;
    const entityGame: EntityGame = await GameService.findOneEntityGame(brand, merchantStartGameTokenData.gameCode);

    let limits: Limits;
    let gameGroupName: string = merchantStartGameTokenData.gameGroup;
    const merchant: Merchant = await MerchantCache.findOne(brand);

    if (!gameGroupName && settings.useMerchantPlayerGameGroup) {
        const mapping: GameGroupInfo
            = await getMerchantPlayerGameGroupService().getSingle(brand.parent, merchantStartGameTokenData.playerCode);

        if (mapping) {
            gameGroupName = mapping.gameGroup.toString();
        }
    }
    const deviceId = request.deviceId;
    const gameFreeBets = await PlayerPromotionService.getGamePromotion(
        brand,
        merchantStartGameTokenData.playerCode,
        merchantStartGameTokenData.currency,
        merchantStartGameTokenData.gameCode,
        PROMO_TYPE.FREEBET,
        settings.sharedPromoEnabled,
        settings.playerBonusesEnabled
    );
    const additionalInfo: AdditionalInfo = {};

    if (gameFreeBets && gameFreeBets.length) {
        additionalInfo.freebets = gameFreeBets;
    }
    const gameTokenInfo = await merchant.getGameTokenInfo(merchantStartGameTokenData,
        merchantStartGameTokenData.currency,
        entityGame,
        deviceId,
        additionalInfo);

    const gameTokenData = gameTokenInfo.gameTokenData as POPAuthTokenData;

    await decorateTokenWithSegmentId(gameTokenInfo, merchant, entityGame, settings);
    decorateTokenData(gameTokenData, merchantStartGameTokenData, settings, entityGame);

    const currency: string = startGameTokenData.playmode === PlayMode.BNS
        ? BONUS_COIN_CURRENCY
        : startGameTokenData.currency;

    const newLimitsDisabled = entityGame.settings?.newLimitsDisabled;

    const shouldExternalLimitsBeUsed = entityGame.game && entityGame.game.type === "external" &&
        settings.useGameProviderLimits;

    if (shouldExternalLimitsBeUsed) {
        limits = {};
    } else {
        const dynamicMaxTotalBetLimit = merchantStartGameTokenData.dynamicMaxTotalBetLimit ||
            gameTokenData.dynamicMaxTotalBetLimit;
        if (settings.newLimitsEnabled && !newLimitsDisabled) {
            const gameGroupId = await getGameGroupId(brand, gameGroupName, settings);

            limits = await getNewLimitsFacade(brand).buildGameLaunch(entityGame.game,
                currency,
                gameGroupId,
                (gameTokenData as POPAuthTokenData).segmentId,
                settings.isMarketplaceSupported,
                undefined,
                false,
                dynamicMaxTotalBetLimit
            );
        } else {
            const dynamicMaxTotalBetLimit = merchantStartGameTokenData.dynamicMaxTotalBetLimit ||
                gameTokenData.dynamicMaxTotalBetLimit;
            const dynamicDefaultTotalBet = merchantStartGameTokenData.dynamicDefaultTotalBet ||
                gameTokenData.dynamicDefaultTotalBet;
            const dynamicMinTotalBetLimit = merchantStartGameTokenData.dynamicMinTotalBetLimit ||
                gameTokenData.dynamicMinTotalBetLimit;
            [limits] = await findPlayerLimits(
                brand,
                entityGame,
                currency,
                gameGroupName,
                settings,
                false,
                dynamicMaxTotalBetLimit,
                dynamicDefaultTotalBet,
                dynamicMinTotalBetLimit
            );
        }
    }

    await validateMerchantStartGame(brand, merchantStartGameTokenData, settings.skipBlockedPlayerValidation);

    const isTestFromToken = gameTokenData.test === undefined ? merchantStartGameTokenData.test : gameTokenData.test;

    let isTestPlayer;
    if (!settings.skipTestPlayerValidation) {
        isTestPlayer = await validateAndGetPlayerTestStatus(
            brand,
            merchantStartGameTokenData,
            isTestFromToken,
            entityGame
        );
        gameTokenData.test = isTestPlayer;
    } else {
        isTestPlayer = isTestFromToken;
    }

    if (!gameTokenData.test) {
        if (brand.isTest) {
            throw new Errors.PlayerTestModeError("brand.isTest = true");
        }
        if (startGameTokenData.playmode === PlayMode.PLAY_MONEY) {
            if (brand.status === EntityStatus.TEST) {
                throw new Errors.PlayerTestModeError("brand.status = test");
            }
            if (entityGame.status === ENTITY_GAME_STATUS.TEST) {
                throw new Errors.PlayerTestModeError("entityGame.status = test");
            }
        }
    }

    if (brand.migrationStatus) {
        await MigrationService.forceMigratePlayer(brand, startGameTokenData.playerCode);
    }

    const gameSettings = await getMerchantGameSettings(brand,
        entityGame,
        settings,
        merchant,
        merchantStartGameTokenData.playmode,
        limits,
        currency
    );

    const merchantRegulation = merchant.params?.regulatorySettings?.merchantRegulation;

    if (merchantRegulation && merchantRegulation.toLowerCase() === SWISS_MERCHANT_REGULATION) {
        // decorate gameTokenData with game version which will be fetched from the gameserver
        gameTokenData.gameVersion = request.gameVersion;
    }

    const playerInfo = await getPlayerInfoService().getPlayerInfo(
        merchantStartGameTokenData.playerCode,
        brand.id,
        settings.skipPlayerInfoRequest,
        entityGame.game.isLiveGame()
    );

    const player: PlayerInfo = {
        code: merchantStartGameTokenData.playerCode || gameTokenData.playerCode,
        currency: merchantStartGameTokenData.currency,
        country: gameTokenData.country || merchantStartGameTokenData.country,
        language: merchantStartGameTokenData.language,
        nickname: playerInfo.nickname || merchantStartGameTokenData.nickname,
        isVip: playerInfo.isVip,
        status: undefined,
        firstName: undefined,
        lastName: undefined,
        email: undefined,
        isTest: isTestPlayer,
        brandId: brand.id,
        isPrivateChatBlock: playerInfo.isPrivateChatBlock,
        isPublicChatBlock: playerInfo.isPublicChatBlock,
        isTracked: playerInfo.isTracked,
        hasWarn: playerInfo.hasWarn,
        nicknameChangeAttemptsLeft: getNicknameChangeAttemptsLeft(playerInfo as any)
    };

    const jurisdiction = await EntityJurisdictionCache.findOne(brand);

    let operatorSiteId;
    if (!settings.skipAvailableSiteRequest) {
        const externalCode = gameTokenInfo.operatorSiteExternalCode;
        const referrer = merchantStartGameTokenData.referrer;
        operatorSiteId = await getAvailableSiteService(brand).getOperatorSiteId(externalCode, referrer);
    }

    const calculatedMaxTotalStake = (limits as SlotGameLimits).maxTotalStake;
    if (calculatedMaxTotalStake && jurisdiction?.settings?.dynamicMaxTotalBetLimitEnabled) {
        gameTokenData.dynamicMaxTotalBetLimit = calculatedMaxTotalStake;
    }

    const response = {
        limits: limits,
        useGameProviderLimits: shouldExternalLimitsBeUsed ? true : undefined,
        settings: gameSettings,
        logoutOptions: merchant.params.gameLogoutOptions,
        gameToken: await TokenUtils.generateGameToken(gameTokenData),
        player: player,
        jrsdSettings: jurisdiction?.settings,
        jurisdictionCode: jurisdiction?.code,
        region: config.region,
        renderType: entityGame.game.historyRenderType,
        operatorSiteId
    };

    const sessionInfo: GameSessionInfo = await createPlayerSession(brand, startGameTokenData, settings, entityGame,
        {
            sharedPromoEnabled: settings.sharedPromoEnabled,
            operatorSiteId,
            rtpDeduction: gameSettings?.rtpConfigurator?.rtpDeduction,
            bonusPaymentMethod: settings.bonusPaymentMethod,
            gameFreeBets,
            playerBonusesEnabled: settings.playerBonusesEnabled
        });

    await decorateStartGameDataWithPhantomPayload({
        startGameResponse: response,
        playerCode: player.code,
        game: entityGame.game,
        brand,
        entitySettings: settings,
        currency: merchantStartGameTokenData.currency,
        isTest: isTestPlayer,
        deviceData: request.deviceData,
        disablePlayerPhantomFeatures: merchantStartGameTokenData.disablePlayerPhantomFeatures
    });
    decorateStartGameDataWithCurrencyReplacement(response, merchant.params, merchantStartGameTokenData.currency);

    let deferredPayments = [];
    if (settings.bonusPaymentMethod === DeferredPaymentMethod.CREDIT) {
        deferredPayments = await defaultDeferredPaymentFacade.get().load(brand.id, startGameTokenData.playerCode);
    }

    const startGameResult = {
        response,
        deferredPayments,
        sessionInfo,
        operatorInfo: await defaultOperatorDetailsRepository.get().findById(brand.id)
    };

    decorateStartGameResultWithMerchantSpecificData(startGameResult, merchant, gameTokenData);

    return startGameResult;
}

export async function validateMerchantStartGame(brand: BrandEntity,
    startGameTokenData: StartGameTokenData,
    skipBlockedPlayerValidation: boolean = false) {

    if (!skipBlockedPlayerValidation
        && await BlockedPlayersService.isMerchantPlayerBlocked(brand.id, startGameTokenData.playerCode)) {
        throw new Errors.PlayerIsSuspended();
    }
    if (startGameTokenData.playmode === "real") {
        await validateEntityBalance(brand, startGameTokenData.currency);
    }
}

export async function validateAndGetPlayerTestStatus(brand: BrandEntity,
    startGameTokenData: StartGameTokenData,
    isTestPlayer: boolean,
    entityGame: EntityGame): Promise<boolean> {

    if (startGameTokenData.playmode !== PlayMode.REAL && startGameTokenData.playmode !== PlayMode.PLAY_MONEY
        && startGameTokenData.playmode !== PlayMode.FUN_BONUS
    ) {
        return undefined;
    }
    if (isTestPlayer) {
        await getMerchantTestPlayerService(brand).add({ code: startGameTokenData.playerCode });
    }
    const player = await merchantTestPlayersCache.findOne(brand, startGameTokenData.playerCode);
    if (player) {
        if (player.endDate && new Date(player.endDate) <= new Date()) {
            throw new Errors.MerchantTestPlayerError(`invalid test player end date ${player.endDate}. 
                    End date should > current date`);
        }
        if (player.source === SOURCE.INTEGRATION && !isTestPlayer) {
            throw new Errors.MerchantTestPlayerError(
                "Player marked as integration test player, but not marked as test by the operator."
            );
        }
        return true;
    }
    if (entityGame.status !== ENTITY_GAME_STATUS.TEST && brand.status !== EntityStatus.TEST) {
        return isTestPlayer;
    }
    throw new Errors.MerchantTestPlayerError("only test player can play in tests game");
}

export function decorateStartGameResultWithMerchantSpecificData(data: StartGameResult<DetailedStartGameResponse>,
    merchant: Merchant,
    gameTokenData: MerchantGameTokenData): void {
    const merchInfo = merchant.toInfo();
    const brandSettings = {} as any;

    if (merchant.params.useMrchHistoryUrlForHistory) {
        brandSettings.omitHistoryURLParams = true;
    }

    if (MERCHANT_POP_TYPES.includes(merchant.type)
        && (MerchantInfoHelper.isItalianRegulation(merchInfo) || MerchantInfoHelper.isSpanishRegulation(merchInfo))) {
        if (!data.extraData) {
            data.extraData = {};
        }

        if (MerchantInfoHelper.isItalianRegulation(merchInfo)) {
            // adds flag for game wrapper to notify it has to use POP insight plugin for Italy
            data.extraData.POPItalyInsight = true;
        } else {
            // adds flag for game wrapper to notify it will be dealing with POP Spain functionality
            data.extraData.POPSpainRegulation = true;
        }

        if (merchant.params.regulatorySettings.operatorName && merchant.params.regulatorySettings.operatorLicenseId) {
            brandSettings.operatorName = merchant.params.regulatorySettings.operatorName;
            brandSettings.operatorLicenseId = merchant.params.regulatorySettings.operatorLicenseId;
        }
    } else if (MerchantInfoHelper.isItalianRegulation(merchInfo) && gameTokenData.regulatoryData) {
        brandSettings.aamsSessionCode = gameTokenData.regulatoryData.aamsSessionCode;
        brandSettings.ropCode = gameTokenData.regulatoryData.ropCode;
    }

    if (Object.keys(brandSettings).length > 0) {
        data.response.brandSettings = brandSettings;
    }
}

function decorateTokenData(tokenData: MerchantGameTokenData, startGameTokenData: MerchantStartGameTokenData,
    settings: EntitySettings, entityGame: EntityGame) {
    if (startGameTokenData.envId) {
        tokenData.envId = startGameTokenData.envId;
    }
    if (tokenData.test === undefined) {
        tokenData.test = startGameTokenData.test || false;
    }
    if (settings.ggrCalculation) {
        tokenData.ggrCalculation = settings.ggrCalculation;
    }
    decorateTokenDataWithGameSpecificData(tokenData, entityGame);
    decorateTokenDataWithRcSpecificData(tokenData, startGameTokenData);
}

function decorateTokenDataWithGameSpecificData(tokenData: GameTokenData, entityGame: EntityGame) {
    const game = entityGame.game;
    const features = game.features;
    if (!features.isFreebetSupported) {
        tokenData.freeBetsDisabled = true;
    }
    tokenData.isMultibet = features.isMultibet === true;
    tokenData.isLiveGame = entityGame.isLiveGame();
    tokenData.forbidOnlineRetries = features.forbidOnlineRetries === true;
    if (entityGame.game.type === GAME_TYPES.external) {
        tokenData.forwardToWrapper = entityGame.settings?.forwardToWrapper === true;
    }
}

function decorateTokenDataWithRcSpecificData(tokenData: GameTokenData, startGameTokenData: StartGameTokenData) {
    if (!isNaN(startGameTokenData.rci)) {
        tokenData.rci = startGameTokenData.rci;
    }
    if (!isNaN(startGameTokenData.rce)) {
        tokenData.rce = startGameTokenData.rce;
    }
}

export async function decorateTokenWithSegmentId(gameTokenInfo: MerchantGameTokenInfo,
    merchant: MerchantInfo,
    entityGame: EntityGame,
    settings: EntitySettings): Promise<void> {
    const isMPSupported = entityGame.game?.features?.isMarketplaceSupported;
    if (!(gameTokenInfo.segmentFilter && isMPSupported && settings.newLimitsEnabled)) {
        return;
    }

    if (MERCHANT_POP_TYPES.includes(merchant.type)) {
        log.info(gameTokenInfo.segmentFilter, "Segment search filter");

        const matchingData = gameTokenInfo.segmentFilter?.matchingData || {};
        const externalPath = gameTokenInfo.segmentFilter?.externalResellerPath;
        const externalId = gameTokenInfo.segmentFilter?.externalId;

        const activeSegment = await getSearchPlayerSegmentService(merchant.type)
            .searchBestMatch(entityGame.id, matchingData, externalPath, externalId);

        if (activeSegment) {
            log.info(`Matched segment: ${activeSegment && activeSegment.id}`);
            gameTokenInfo.gameTokenData.segmentId = activeSegment.id;
        } else {
            const anySegmentExists = await getSearchPlayerSegmentService(merchant.type)
                .segmentExists(entityGame.id, matchingData, externalPath, externalId);

            if (anySegmentExists) {
                throw new Errors.LimitsForCurrencyNotFound(gameTokenInfo.gameTokenData.currency);
            }
            log.info("POP matched segment: default");
        }
    }
}

function decorateStartGameDataWithCurrencyReplacement(data: DetailedStartGameResponse,
    params: MerchantParams,
    playerCurrency: string): DetailedStartGameResponse {
    if (params.gameClientCurrencyReplacement && params.gameClientCurrencyReplacement[playerCurrency]) {
        data.currencyReplacement = params.gameClientCurrencyReplacement[playerCurrency];
    }
    return data;
}

interface GetPhantomPayload {
    playerCode: string;
    game: Game;
    brand: BrandEntity;
    currency: string;
    isTest: boolean;
    deviceData: any;
}

interface DecorateWithPhantomPayload extends GetPhantomPayload {
    startGameResponse: DetailedStartGameResponse;
    entitySettings: EntitySettings;
    disablePlayerPhantomFeatures?: boolean;
}

async function decorateStartGameDataWithPhantomPayload(data: DecorateWithPhantomPayload): Promise<void> {

    if (data.disablePlayerPhantomFeatures ||
        !data.entitySettings.enablePhantomFeatures ||
        !config.phantom.enablePhantomFeatures) {
        return;
    }

    const phantomPayload = await getPhantomPayload(data);

    if (!phantomPayload) {
        return;
    }
    data.startGameResponse.phantom = phantomPayload;
}

export async function doStartGame(request: StartGameRequest,
    ip?: string, referrer?: string): Promise<StartGameResult<DetailedStartGameResponse>> {

    let startGameTokenData: StartGameTokenData;
    try {
        startGameTokenData = await TokenUtils.verifyStartGameToken(request.startGameToken);
    } catch (err) {
        if (err instanceof token.TokenVerifyException) {
            return Promise.reject(new Errors.StartGameTokenError());
        }

        if (err instanceof token.TokenExpiredException) {
            return Promise.reject(new Errors.StartGameTokenExpired());
        }

        return Promise.reject(err);
    }

    const brand: BrandEntity = await getBrand(startGameTokenData.brandId);
    if (brand.underMaintenance()) {
        return Promise.reject(new Errors.EntityUnderMaintenanceError());
    }

    const settings: EntitySettings = await getEntitySettings(brand.path);
    if (!settings.skipAvailableSiteRequest) {
        await checkSiteIsAuthorized(brand, referrer);
    }
    await validateEntityEnvironment(brand, startGameTokenData.envId);

    let entityGame: EntityGame;
    if (settings.cacheEntityGames) {
        entityGame = await EntityGameCache.findOne(brand, startGameTokenData.gameCode);
    } else {
        entityGame = await GameService.findOneEntityGame(brand, startGameTokenData.gameCode);
    }
    if (entityGame.status === ENTITY_GAME_STATUS.HIDDEN) {
        throw new Errors.GameSuspendedError(entityGame.game.code);
    }

    if (startGameTokenData.playmode === PlayMode.FUN_BONUS) {
        let isFunModeNotSupported = false;
        if (entityGame.game?.features?.isFunModeNotSupported !== undefined) {
            isFunModeNotSupported = entityGame.game?.features?.isFunModeNotSupported;
        } else if (settings.isFunModeNotSupported !== undefined) {
            isFunModeNotSupported = settings.isFunModeNotSupported;
        }
        if (isFunModeNotSupported) {
            return Promise.reject(new Errors.GameLaunchForbidden());
        }
    }

    let countrySource: CountrySource;
    try {
        const isLiveGame = entityGame.game.type === GAME_TYPES.live;
        countrySource = await getCountrySource({
            brand,
            entitySettings: settings,
            playerCode: startGameTokenData.playerCode,
            countryCode: startGameTokenData.operatorCountry,
            ip
        }, settings.skipPlayerInfoRequest, isLiveGame);

        await validateGameCountryRestrictions(
            entityGame,
            brand,
            settings,
            startGameTokenData.currency,
            countrySource
        );
    } catch (err) {
        await sendPlayerBlockingAnalytics(err, countrySource, {
            initiator: "game-start",
            playerCode: startGameTokenData.playerCode,
            currencyCode: startGameTokenData.currency,
            ip,
            operatorCountryCode: startGameTokenData.operatorCountry,
            brandId: brand.id,
            gameCode: entityGame.game?.code
        });

        return Promise.reject(err);
    }

    const playerResponsibleGamingSettings =
        await getPlayerResponsibleGamingService(brand).validatePlayerRestrictions(startGameTokenData.playerCode);

    const playedFromCountry = await getCountryAndRegionCodesByIp(ip);
    const operatorPlayerCountry = startGameTokenData.country;

    let result: StartGameResult<DetailedStartGameResponse>;
    if (brand.isMerchant) {
        if (ip && !startGameTokenData.operatorCountry && !startGameTokenData.country) {
            const whitelisted = Boolean(countrySource.code) === false;
            if (whitelisted) {
                startGameTokenData.country = await EntityHelper.getDefaultCountry(brand, settings);
            } else {
                startGameTokenData.country = playedFromCountry;
            }
        }
        result = await authenticateMerchantGame(brand, startGameTokenData, settings, request);
    } else {
        result = await authenticateGame(brand, startGameTokenData, settings, request);
    }
    const startGameResponse = result.response;

    if (playedFromCountry) {
        startGameResponse.playedFromCountry = playedFromCountry;
    }
    if (operatorPlayerCountry) {
        startGameResponse.operatorPlayerCountry = operatorPlayerCountry;
    }

    decorateStartGameDataWithResponsibleGaming(playerResponsibleGamingSettings, startGameResponse);

    const entity = await EntityCache.findOne({ id: entityGame.entityId });
    const gameVersion = await getGameClientVersionService().getGameClientVersionItem(entity, entityGame);
    await decorateStartGameDataWithBrandSettings(brand,
        settings,
        entityGame,
        startGameResponse,
        startGameTokenData.playmode,
        !!startGameTokenData.test,
        gameVersion,
        startGameTokenData.currency);

    decorateStartGameDataWithGameSettings(entityGame, startGameResponse, gameVersion);
    decorateStartGameDataWithJurisdictionSettings(startGameResponse);

    decorateStartGameDataWithJackpotSettings(entityGame, settings, startGameResponse, startGameTokenData.playmode);
    decorateStartGameDataWithRTP(entityGame, settings, startGameResponse);
    decorateStartGameDataWithBrandBrandEntity(startGameResponse, brand);

    localizeGameName(startGameResponse, entityGame, request.language);

    upsertRecentlyGame(
        startGameTokenData.brandId,
        startGameTokenData.playerCode || result.response.player.code,
        startGameTokenData.gameCode
    );

    return result;
}

export function decorateStartGameDataWithRTP(entityGame: EntityGame,
    entitySettings: EntitySettings,
    data: DetailedStartGameResponse) {
    const showRTP: boolean | undefined = data.jrsdSettings?.showRTP;
    const overrideShowRTP: boolean | undefined = entitySettings?.clientFeatures?.showRTPOverrideJrsd;
    const show: boolean = overrideShowRTP === undefined ? showRTP : overrideShowRTP;
    if (show) {
        const rtpDeduction: number = data.settings?.rtpConfigurator?.rtpDeduction || 0;
        const features: GameFeatures = entityGame?.game?.features;
        let totalRtp = 0;
        let totalRTPRange;
        if (features.baseRTP) {
            const baseRTP = roundNumber(features.baseRTP - rtpDeduction);
            data.settings.baseRTP = baseRTP;
            totalRtp = baseRTP;
        }
        if (features.baseRTPRange) {
            data.settings.baseRTPRange = {
                min: roundNumber(features.baseRTPRange.min - rtpDeduction),
                max: roundNumber(features.baseRTPRange.max - rtpDeduction)
            };
            totalRTPRange = { ...data.settings.baseRTPRange };
        }
        if (features.jpRTP) {
            if (totalRTPRange) {
                totalRTPRange.min = roundNumber(totalRTPRange.min + features.jpRTP);
                totalRTPRange.max = roundNumber(totalRTPRange.max + features.jpRTP);
            } else if (totalRtp) {
                totalRtp = roundNumber(totalRtp + features.jpRTP);
            }
            data.settings.jpRTP = features.jpRTP;
        }

        if (data.settings.mwjpRTP) {
            if (totalRTPRange) {
                totalRTPRange.min = roundNumber(totalRTPRange.min + data.settings.mwjpRTP);
                totalRTPRange.max = roundNumber(totalRTPRange.max + data.settings.mwjpRTP);
            } else if (totalRtp) {
                totalRtp = roundNumber(totalRtp + data.settings.mwjpRTP);
            }
        } else if (data.settings.iwjpRTP) {
            if (totalRTPRange) {
                totalRTPRange.min = roundNumber(totalRTPRange.min + data.settings.iwjpRTP);
                totalRTPRange.max = roundNumber(totalRTPRange.max + data.settings.iwjpRTP);
            } else if (totalRtp) {
                totalRtp = roundNumber(totalRtp + data.settings.iwjpRTP);
            }
        }
        if (totalRTPRange) {
            data.settings.totalRTPRange = totalRTPRange;
        } else if (totalRtp) {
            data.settings.totalRTP = totalRtp;
        }
        if (features.featuresRTP) {
            data.settings.featuresRTP = {};
            for (const featureName of Object.keys(features.featuresRTP)) {
                const feature = features.featuresRTP[featureName];
                const RTP: number = feature.rtpReducer ? roundNumber(feature.RTP - rtpDeduction) : feature.RTP;
                const mwjpRTP = data.settings.mwjpRTP || 0;
                const iwjpRTP = data.settings.iwjpRTP || 0;
                const totalRTP = roundNumber(RTP + (features.jpRTP || 0) + mwjpRTP + iwjpRTP);
                data.settings.featuresRTP[featureName] = { RTP, totalRTP };
            }
        }
    }
}

function decorateStartGameDataWithResponsibleGaming(playerResponsibleSettings: PlayerResponsibleGamingImpl,
    data: DetailedStartGameResponse) {
    if (playerResponsibleSettings && playerResponsibleSettings.hasRealityCheck()) {
        if (data.jrsdSettings) {
            data.jrsdSettings["realityCheck"] = playerResponsibleSettings.getRealityCheck();
        } else {
            data.jrsdSettings = {
                realityCheck: playerResponsibleSettings.getRealityCheck()
            };
        }
    }
}

async function getCountryAndRegionCodesByIp(ip?: string) {
    if (ip) {
        try {
            return await getIpLocationService().getCountryAndRegionCodesByIp(ip);
        } catch {
            return;
        }
    }
}

function decorateStartGameDataWithBrandBrandEntity(data: DetailedStartGameResponse,
    brand: BrandEntity): DetailedStartGameResponse {
    data.brandInfo = {
        name: brand.name,
        title: brand.title || ""
    };
    return data;
}

async function getStartGameTokenData(request: StartFunGameRequest) {

    let startGameTokenData: StartGameTokenData;

    if (typeof request.startGameToken === "string") {
        startGameTokenData = await TokenUtils.verifyStartGameToken(request.startGameToken);
    } else {
        const funStartGameToken = request.startGameToken as FunStartGameToken;
        startGameTokenData = {
            playerCode: funStartGameToken.playerCode,
            gameCode: funStartGameToken.gameCode,
            brandId: funStartGameToken.brandId,
            currency: funStartGameToken.currency,
            envId: funStartGameToken.envId,
            playmode: PlayMode.FUN,
            providerCode: "",
            providerGameCode: "",
            gameGroup: funStartGameToken.gameGroup
        } as any;
        if (funStartGameToken.jCode) {
            startGameTokenData["jCode"] = funStartGameToken.jCode;
        }
    }

    startGameTokenData.playerCode = startGameTokenData.playerCode || `player${+new Date()}`;
    return startGameTokenData;
}

export async function doStartFunGame(request: StartFunGameRequest): Promise<StartGameResult> {

    const startGameTokenData = await getStartGameTokenData(request);
    const entity: BrandEntity = await EntityCache.findById(startGameTokenData.brandId) as BrandEntity;
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }
    if (entity.underMaintenance()) {
        return Promise.reject(new Errors.EntityUnderMaintenanceError());
    }

    await validatePlayerRestrictionsForFunGame(entity, startGameTokenData);
    await validateEntityEnvironment(entity, startGameTokenData.envId);

    const entityGame: EntityGame = await GameService.findOneEntityGame(entity, startGameTokenData.gameCode);
    const settings: EntitySettings = await getEntitySettings(entity.path);

    const isTokenCastable = typeof request.startGameToken === "string";
    const skipPlayerInfoRequest = settings.skipPlayerInfoRequest;
    const isLiveGame = entityGame.game.type === GAME_TYPES.live;

    const player: PlayerInfo = await getPlayerInfoForFunGame(entity, startGameTokenData, isTokenCastable, skipPlayerInfoRequest, isLiveGame);

    if (entityGame.status === ENTITY_GAME_STATUS.HIDDEN) {
        throw new Errors.GameSuspendedError(entityGame.game.code);
    }

    let isFunModeNotSupported = false;
    if (entityGame.game?.features?.isFunModeNotSupported !== undefined) {
        isFunModeNotSupported = entityGame.game?.features?.isFunModeNotSupported;
    } else if (settings.isFunModeNotSupported !== undefined) {
        isFunModeNotSupported = settings.isFunModeNotSupported;
    }
    if (isFunModeNotSupported) {
        return Promise.reject(new Errors.GameLaunchForbidden());
    }

    const startGameData: DetailedStartGameResponse = await authenticateFunGame(
        entity,
        startGameTokenData,
        player,
        settings,
        entityGame,
        request.deviceId
    );

    decorateStartGameDataWithBrandBrandEntity(startGameData, entity);

    const gameVersion = await getGameClientVersionService().getGameClientVersionItem(entity, entityGame);
    await decorateStartGameDataWithBrandSettings(entity,
        settings,
        entityGame,
        startGameData,
        startGameTokenData.playmode,
        !!startGameTokenData.test,
        gameVersion,
        startGameTokenData.currency);
    decorateStartGameDataWithGameSettings(entityGame, startGameData, gameVersion);
    decorateStartGameDataWithJurisdictionSettings(startGameData);
    decorateStartGameDataWithRTP(entityGame, settings, startGameData);
    localizeGameName(startGameData, entityGame, request.language);

    return { response: startGameData };
}

async function validatePlayerRestrictionsForFunGame(entity: BaseEntity,
    startGameTokenData: StartGameTokenData): Promise<void> {
    if (entity.isBrand()) {
        await getPlayerResponsibleGamingService(entity).validatePlayerSelfExclusionRestriction(
            startGameTokenData.playerCode.toString());
    }
}

async function getPlayerInfoForFunGame(entity: BaseEntity,
    startGameTokenData: StartGameTokenData,
    isTokenCastable: boolean,
    skipPlayerInfoRequest: boolean,
    isLiveGame: boolean): Promise<PlayerInfo> {

    const playerInfoData = await getPlayerInfoService().getPlayerInfo(
        startGameTokenData.playerCode,
        startGameTokenData.brandId,
        skipPlayerInfoRequest,
        isLiveGame
    );

    let playerInfo = {
        nickname: playerInfoData.nickname,
        isVip: playerInfoData.isVip,
        isTracked: playerInfoData.isTracked,
        isPrivateChatBlock: playerInfoData.isPrivateChatBlock,
        isPublicChatBlock: playerInfoData.isPublicChatBlock,
        hasWarn: playerInfoData.hasWarn,
        nicknameChangeAttemptsLeft: getNicknameChangeAttemptsLeft(playerInfoData as any)
    } as PlayerInfo;

    if (entity.isBrand()) {
        if (!isTokenCastable) {
            return;
        }
        const brand = entity as BrandEntity;
        if (brand.isMerchant) {
            const tokenData = startGameTokenData as MerchantStartGameTokenData;
            playerInfo = {
                ...playerInfo,
                code: tokenData.playerCode,
                currency: tokenData.currency,
                country: tokenData.country,
                language: tokenData.language,
                status: undefined,
                isTest: tokenData.test,
                brandId: startGameTokenData.brandId,
                brandTitle: brand.title,
            };
        } else {
            const player: Player = await getBrandPlayerService().findOneWithGameGroup(brand,
                { code: startGameTokenData.playerCode });
            playerInfo = await player.toInfo();
        }
    } else {
        playerInfo = {
            ...playerInfo,
            code: startGameTokenData.playerCode,
            currency: startGameTokenData.currency,
            country: undefined,
            language: undefined,
            status: undefined,
            isTest: startGameTokenData.test,
            brandId: startGameTokenData.brandId,
            brandTitle: entity.title
        };
    }
    return playerInfo;
}

export async function verifyGameToken(gameToken: string): Promise<GameTokenData> {
    try {
        return await TokenUtils.verifyGameToken(gameToken);
    } catch (err) {
        if (err instanceof token.TokenVerifyException) {
            return Promise.reject(new Errors.GameTokenError());
        }
        if (err instanceof token.TokenExpiredException) {
            return Promise.reject(new Errors.GameTokenExpired());
        }
        return Promise.reject(err);
    }
}

function localizeGameName(startGameData: DetailedStartGameResponse, entityGame: EntityGame, language?: string) {
    if (!language) {
        return;
    }

    const localizedGameName = entityGame?.game?.info?.[language.toLowerCase()]?.name;

    if (localizedGameName) {
        startGameData.localizedGameName = localizedGameName;
    }
}

export function decorateStartGameDataWithGameSettings(entityGame: EntityGame,
    data: DetailedStartGameResponse,
    gameVersion: GameVersionInfo) {

    const clientFeaturesFromGameVersion = gameVersion && gameVersion.clientFeatures || {};
    const clientFeaturesFromGame = entityGame.game.clientFeatures as GameClientFeatures || {};

    const clientFeatures = Object.assign(clientFeaturesFromGame, clientFeaturesFromGameVersion);

    data.gameSettings = data.gameSettings
        ? { ...data.gameSettings, ...clientFeatures }
        : clientFeatures || {};

    if (entityGame.game.features.isMultibet) {
        data.gameSettings.isMultibet = entityGame.game.features.isMultibet;
    }

    if (entityGame.game.features.isFreebetSupported && !isNaN(+entityGame.game.totalBetMultiplier)) {
        data.gameSettings.totalBetMultiplier = +entityGame.game.totalBetMultiplier;
    }

    if ("gamble" in entityGame.game.features) {
        data.gameSettings.gamble = entityGame.game.features.gamble;
    }

    if ("highestWin" in entityGame.game.features) {
        data.gameSettings.highestWin = entityGame.game.features.highestWin;
    }
}

function decorateStartGameDataWithJurisdictionSettings(data: DetailedStartGameResponse) {
    if (data.jurisdictionCode === JurisdictionCodes.GREECE &&
        data.jrsdSettings &&
        data.gameSettings.buyFeatureJrsdGrLegacy === false) {
        data.jrsdSettings.buyFeature = false;
    }
    if (data.jurisdictionCode === JurisdictionCodes.PORTUGAL &&
        data.jrsdSettings &&
        data.gameSettings.buyFeatureJrsdPtLegacy === true) {
        data.jrsdSettings.buyFeature = true;
    }
}

export async function decorateStartGameDataWithBrandSettings(brand: BrandEntity, settings: EntitySettings,
    entityGame: EntityGame,
    data: DetailedStartGameResponse,
    playmode: string,
    isTestMode: boolean,
    gameVersion: GameVersionInfo,
    currency: string): Promise<void> {

    const clientFeaturesFromGameVersion = gameVersion && gameVersion.clientFeatures || {};
    const clientFeaturesFromSettings = settings.clientFeatures as BrandClientFeatures || {};


    const clientFeatures = Object.assign(clientFeaturesFromSettings, clientFeaturesFromGameVersion);
    await exchangeBuyInAndAnteBetFeatures(clientFeatures, currency);
    await replaceLiveStreamingDomain(clientFeatures, brand);

    const fullscreen = clientFeatures.fullscreen;

    data.brandSettings = data.brandSettings
        ? { ...data.brandSettings, ...clientFeatures }
        : clientFeatures;

    if (settings.regulatoryLinks) {
        data.brandSettings.regulatoryLinks = settings.regulatoryLinks;
    }

    data.brandSettings.fullscreen = typeof fullscreen === "boolean" ? fullscreen : true;
    if (isTestMode && !(data.brandSettings.showTestModePopup === false)) {
        data.brandSettings.showTestModePopup = true;
    }

    if (!data.settings) {
        data.settings = {};
    }

    if (!settings.finalizationSupport && entityGame.game.features.offlineWin) {
        data.settings.brandFinalizationType = BrandFinalizationType.FORCE_FINISH;
    }

    if (playmode === PlayMode.REAL || playmode === PlayMode.PLAY_MONEY || playmode === PlayMode.BNS || playmode === PlayMode.FUN_BONUS) {
        if (settings.maxPaymentRetryAttempts !== undefined) {
            data.settings.maxPaymentRetryAttempts = settings.maxPaymentRetryAttempts;
        } else if (entityGame.game.features.offlineWin) {
            data.settings.maxPaymentRetryAttempts = config.offlineWinGameProvider.defaultMaxRetryAttempts;
        }

        if (settings.minPaymentRetryTimeout !== undefined) {
            data.settings.minPaymentRetryTimeout = settings.minPaymentRetryTimeout;
        } else if (entityGame.game.features.offlineWin) {
            data.settings.minPaymentRetryTimeout = config.offlineWinGameProvider.defaultMinRetryTimeout;
        }
    }

    if (playmode === PlayMode.REAL || playmode === PlayMode.PLAY_MONEY || playmode === PlayMode.FUN_BONUS) {
        if (settings.contributionPrecision) {
            data.settings.contributionPrecision = settings.contributionPrecision;
        }

        if (settings.splitPayment !== undefined) {
            data.settings.splitPayment = settings.splitPayment;
        }

        if (settings.autoPlaySettings) {
            data.settings.autoSpinsOptions = settings.autoPlaySettings;
        }

        if (settings.jpTickerRefreshPeriod) {
            data.settings.jpTickerRefreshPeriod = settings.jpTickerRefreshPeriod;
        }

        const roundExpireAt = entityGame.settings?.roundExpireAt || settings.roundExpireAt;
        if (roundExpireAt !== undefined) {
            data.settings.roundExpireAt = roundExpireAt;
            const finalizationRetryPolicy = entityGame.settings?.finalizationRetryPolicy || settings.finalizationRetryPolicy;
            if (finalizationRetryPolicy) {
                data.settings.finalizationRetryPolicy = finalizationRetryPolicy;
            }
        }

        if (settings.deferredContribution !== undefined) {
            data.settings.deferredContribution = settings.deferredContribution;
        }

        if (settings.logoutControl) {
            data.settings.logoutControl = settings.logoutControl;
        }

        if (settings.hideBalanceBeforeAndAfter) {
            data.settings.hideBalanceBeforeAndAfter = settings.hideBalanceBeforeAndAfter;
        }

        if (settings.skipPendingPaymentReAuthentication) {
            data.settings.skipPendingPaymentReAuthentication = settings.skipPendingPaymentReAuthentication;
        }

        if (settings.addBetAmountOnFreeBetRollback) {
            data.settings.addBetAmountOnFreeBetRollback = settings.addBetAmountOnFreeBetRollback;
        }
    }
}

async function exchangeBuyInAndAnteBetFeatures(
    clientFeatures: ClientFeatures,
    targetCurrency: string
): Promise<void> {
    const currency = Currencies.get(targetCurrency);

    if (!currency.toEURMultiplier) {
        return;
    }

    if (clientFeatures.maxBuyInStake) {
        clientFeatures.maxBuyInStake = clientFeatures.maxBuyInStake * currency.toEURMultiplier;
    }

    if (clientFeatures.maxAnteBetStake) {
        clientFeatures.maxAnteBetStake = clientFeatures.maxAnteBetStake * currency.toEURMultiplier;
    }
}

function decorateStartGameDataWithJackpotSettings(game: EntityGame,
    entitySettings: EntitySettings,
    data: DetailedStartGameResponse,
    playmode: PlayMode) {
    const jackpots: JackpotSettings[] = [];

    if (data.settings.jackpotId) {
        for (const jackpotId of Object.values(data.settings.jackpotId)) {
            const allowMissingContribution = !!game.game?.features?.allowMissingContribution;
            jackpots.push({ ...DEFAULT_GAME_JACKPOT_SETTINGS, allowMissingContribution, id: jackpotId });
        }
    }

    if (data.phantom) {
        for (const phantomJackpot of data.phantom.jackpots) {
            if (jackpots.find((jp) => jp.id === phantomJackpot.jackpotId)) {
                continue;
            }
            if (phantomJackpot.rtp) {
                if (phantomJackpot.type === "instant") {
                    data.settings.iwjpRTP = roundNumber((data.settings.iwjpRTP || 0) + phantomJackpot.rtp * 100);
                } else {
                    data.settings.mwjpRTP = roundNumber((data.settings.mwjpRTP || 0) + phantomJackpot.rtp * 100);
                }
            }
            const allowMissingContribution = !!game.game?.features?.allowMissingContribution;
            jackpots.push({
                ...DEFAULT_PHANTOM_JACKPOT_SETTINGS,
                allowMissingContribution,
                ...phantomJackpot,
                type: !phantomJackpot.type ? "mwjp" : phantomJackpot.type, // if undefined, consider it's mwjp
                id: phantomJackpot.jackpotId
            });
        }
    }

    if (playmode === PlayMode.REAL) {
        const marketing = game.settings?.marketing || entitySettings.marketing;
        if (game.game.features?.supportsMarketingJP && marketing) {
            for (const mrkt of marketing.contributions) {
                if (jackpots.find((jp) => jp.id === mrkt.jackpotId)) {
                    continue;
                }
                const allowMissingContribution = !!game.game.features?.allowMissingContribution;
                jackpots.push({
                    ...DEFAULT_MARKETING_JACKPOT_SETTINGS,
                    allowMissingContribution,
                    ...mrkt,
                    id: mrkt.jackpotId
                });
            }
        }
        delete data.settings.marketing;
    }

    if (jackpots.length) {
        data.jackpots = jackpots;
    }
}

const operationIdNameMap = new Map<string, number>([
    ["bet", OPERATION_ID.BET],
    ["win", OPERATION_ID.WIN],
    ["transfer", OPERATION_ID.BET], // transfer here means money transfers to/from player accounts,
    // which go with BET operation id
    ["rollback", OPERATION_ID.ROLLBACK],
    ["retention-tools-debit", OPERATION_ID.RETENTION_TOOLS_DEBIT],
    ["retention-tools-credit", OPERATION_ID.RETENTION_TOOLS_CREDIT],
    ["freebet", OPERATION_ID.FREE_BET],
    ["coin-transfer", OPERATION_ID.TRANSFER] // fu-fish and similar transfers of money
]);

/**
 * Return transaction information
 * @param entityId
 * @param extTransactionId
 * @param operationIdName
 * @returns {Promise<GetTransactionInfoResponse>}
 */
export async function getTransactionInfo(entityId: number,
    extTransactionId: string,
    operationIdName: string): Promise<GetTransactionInfoResponse> {
    const paymentData = await getEntityPlayerFinanceService().checkStatusAndGetPaymentOrder(entityId,
        undefined, extTransactionId);
    if (!paymentData) {
        return {
            status: "absent" as TrxStatus
        };
    }

    const trxId = paymentData.trxId;
    const playerBalanceAfter = +paymentData.playerBalanceAfter;

    try {
        const operationId = operationIdNameMap.get(operationIdName) || 0;
        let transaction = await WalletFacade.findCommittedTransaction(trxId, operationId);
        // win and bet may come under the same bet operation id if they were processed together
        if (!transaction && operationId === OPERATION_ID.WIN) {
            // so we re-try win search with bet operation id
            transaction = await WalletFacade.findCommittedTransaction(trxId, OPERATION_ID.BET);
        }

        const result: GetTransactionInfoResponse = {
            paymentStatus: paymentData.orderStatus,
            status: "committed"
        };

        if (paymentData.orderStatus === DECLINED) {
            result.declineReason = paymentData.declineReason;
        }

        if (!transaction) {
            result.status = "absent";
            return result;
        }

        result.playerBalanceAfter = playerBalanceAfter;
        return result;

    } catch (err) {
        if (err instanceof WalletErrors.TransactionIsProcessing) {
            return {
                status: "processing",
                paymentStatus: paymentData.orderStatus,
                playerBalanceAfter
            };
        }

        return Promise.reject(err);
    }
}

function verifyTokensData(
    startGameTokenData: StartGameTokenData,
    gameTokenData: GameTokenData,
    req: PlayerGameInfoRequest
) {
    let errorMsg;
    if (gameTokenData.brandId !== startGameTokenData.brandId) {
        errorMsg = `wrong values (${startGameTokenData.brandId}, ${gameTokenData.brandId}) for brandId key`;
    } else if (gameTokenData.playerCode !== startGameTokenData.playerCode) {
        errorMsg = `wrong values (${startGameTokenData.playerCode}, ${gameTokenData.playerCode}) for playerCode key`;
    } else if (gameTokenData.currency !== startGameTokenData.currency) {
        errorMsg = `wrong values (${startGameTokenData.currency}, ${gameTokenData.currency}) for currency key`;
    } else if (gameTokenData.gameCode !== startGameTokenData.gameCode && req.request !== "choose-game") {
        errorMsg = `wrong values (${startGameTokenData.gameCode}, ${gameTokenData.gameCode}) for gameCode key`;
    }

    if (errorMsg) {
        throw new Errors.TokensVerifyError(errorMsg);
    }
}

export async function getPlayerGameURLInfo(req: PlayerGameInfoRequest, ip?: string): Promise<PlayerGameURLInfo> {

    const startGameTokenData = req.gameToken ?
        await TokenUtils.parseStartGameTokenWithoutExpiration(req.startGameToken) :
        await TokenUtils.parseStartGameToken(req.startGameToken);

    if (req.gameToken) {
        const gameTokenData = await verifyGameToken(req.gameToken);
        verifyTokensData(startGameTokenData, gameTokenData, req);
        req.gameTokenData = gameTokenData as MerchantGameTokenData;
    }

    const entity: BaseEntity = await EntityCache.findOne({ id: startGameTokenData.brandId });

    // add gameId (gameCode) if it is missing based on providerGameCode
    if (!req.gameId) {
        const games = await GameService.getAvailableGameCodesByProviderGameCode(req.providerGameCode,
            startGameTokenData.providerCode, 1);
        if (games.length === 0) {
            throw new Errors.GameNotFoundError(
                `providerGameCode: ${req.providerGameCode} and providerCode: ${startGameTokenData.providerCode}`
            );
        }
        req.gameId = games[0];
    }

    if (startGameTokenData.playmode === PlayMode.FUN) {
        return UrlManager.getAnonymousGameURL({
            keyEntity: entity,
            gameCode: req.gameId,
            ip,
            playerCode: startGameTokenData.playerCode,
            ignoreWebSiteWhitelistedCheck: true
        });
    }
    return getRealGameUrl(entity, startGameTokenData, req, ip);
}

async function getRealGameUrl(entity: BaseEntity, gameTokenData: StartGameTokenData,
    req: PlayerGameInfoRequest, ip?: string) {
    const brand: BrandEntity = entity as BrandEntity;

    if (brand.isMerchant) {
        const tokenData = gameTokenData as MerchantStartGameTokenData;
        const merchantData: MerchantGameInitRequest = {
            merchantType: tokenData.merchantType,
            merchantCode: tokenData.merchantCode,
            playmode: req.playmode || tokenData.playmode,
            language: tokenData.language,
            gameCode: req.gameId,
            previousStartTokenData: tokenData,
            previousGameTokenData: req.gameTokenData
        };
        return getMerchantService().getGameUrl(merchantData, { ip, ignoreWebSiteWhitelistedCheck: true });
    } else {
        return UrlManager.getPlayerGameURL({
            keyEntity: brand,
            playerCode: gameTokenData.playerCode,
            gameCode: req.gameId,
            playMode: req.playmode || gameTokenData.playmode,
            ip,
            ignoreWebSiteWhitelistedCheck: true,
            aamsSessionId: req.aamsSessionId,
            aamsParticipationCode: req.aamsParticipationCode
        });
    }
}

export async function getGames(req: GamesListRequest): Promise<GamesListResponse> {
    const gameTokenData: StartGameTokenData = await TokenUtils.parseStartGameToken(req.startGameToken);

    const entity: BaseEntity = await EntityCache.findOne({ id: gameTokenData.brandId });
    await validateEntityEnvironment(entity, gameTokenData.envId);

    const games = await GameService.getAvailableGameCodes(entity, req.filter);
    return { games };
}

export async function getGameGroupId(entity: BrandEntity,
    gameGroupName: string,
    settings: EntitySettings): Promise<number> {
    const gameGroupService = getGameGroupService();
    const gameGroupDBItem = await gameGroupService
        .findOneForPlayer(entity, gameGroupName, settings);

    if (gameGroupDBItem) {
        return gameGroupDBItem.get("id");
    }
}

async function getPhantomPayload({
    brand, game, currency,
    isTest, deviceData, playerCode
}: GetPhantomPayload): Promise<PhantomPayload> {
    const entitySettings = new EntitySettingsService(brand);
    const settings = await entitySettings.get();

    const service = getPhantomService(settings);
    const jackpotsRequest: GetPhantomJackpotsRequest = {
        playerCode: playerCode,
        brandId: brand.id,
        gameCode: game.code,
        currency: currency,
        customerId: settings.env || "skywindgroup",
        isTest: simplisticBooleanParam(isTest),
        deviceData
    };

    return service.getJackpots(jackpotsRequest);
}

function getNicknameChangeAttemptsLeft(playerInfo: PlayerInfo): number | undefined {
    return playerInfo && typeof playerInfo.nicknameChangeAttempts === "number"
        ? config.playerNicknameChangeAttemptsLimit - playerInfo.nicknameChangeAttempts
        : undefined;
}

export async function replaceLiveStreamingDomain(clientFeatures: BrandClientFeatures, brand: BrandEntity) {
    const streamFileds = ["streamCommonUrl", "streamWebsocket", "streamHls", "streamProgressive"];
    const REGEXP = new RegExp("{liveStreamingDomain}", "g");
    const fileds: string[] = [];
    for (const field of streamFileds) {
        if (clientFeatures[field] && clientFeatures[field].match(REGEXP)) {
            fileds.push(field);
        }
    }
    if (fileds.length) {
        const domain = await getEntityStaticDomainService().pick(brand, StaticDomainType.LIVE_STREAMING);
        if (domain?.domain) {
            for (const field of fileds) {
                clientFeatures[field] = clientFeatures[field].replace(REGEXP, domain.domain);
            }
        }
    }
}
