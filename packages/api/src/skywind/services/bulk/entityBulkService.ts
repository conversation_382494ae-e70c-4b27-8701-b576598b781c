import { BulkServiceImpl } from "./bulkService";
import {
    BULK_OPERATION_ACTION,
    ENTITY_BULK_OPERATION_TYPE,
    EntityBulkOperation,
    EntityBulkOperationResult
} from "../../entities/bulk";
import { ApplicationLock, ApplicationLockId } from "../../utils/applicationLock";
import { Transaction } from "sequelize";
import { EntityExecutorFactory } from "./bulkOperationExecutorFactory";
import { BaseEntity } from "../../entities/entity";
import EntitySettingsService from "../settings";
import * as Errors from "../../errors";
import config from "../../config";
import logger from "../../utils/logger";

const log = logger("entity-bulk-service");

export class EntityBulkService extends BulkServiceImpl<EntityBulkOperation, EntityBulkOperationResult> {

    constructor() {
        super(new EntityExecutorFactory());
    }

    public async process(entity: BaseEntity,
                         operations: EntityBulkOperation[]): Promise<EntityBulkOperationResult[]> {
        // Check if operations require cross-GS migration batching
        if (this.requiresCrossGsBatching(entity, operations)) {
            return this.processCrossGsOperations(entity, operations);
        }
        
        return super.process(entity, operations);
    }

    protected async beforeExecute(operations: EntityBulkOperation[], transaction: Transaction): Promise<void> {
        if (this.shouldLockUpdateDynamicDomain(operations)) {
            await ApplicationLock.lock(transaction, ApplicationLockId.UPDATE_DYNAMIC_DOMAIN);
        }
    }

    protected async afterExecute(...args): Promise<void> { /* tslint:disable-line */
    }

    private shouldLockUpdateDynamicDomain(operations: EntityBulkOperation[]): boolean {
        return !!operations.find(
            op => op.action === BULK_OPERATION_ACTION.SET &&
                op.item.type === ENTITY_BULK_OPERATION_TYPE.DYNAMIC);
    }

    private requiresCrossGsBatching(entity: BaseEntity, operations: EntityBulkOperation[]): boolean {
        // Check if any operations involve dynamic domain changes that may require cross-GS migration
        const dynamicDomainOps = operations.filter(op => op.item.type === ENTITY_BULK_OPERATION_TYPE.DYNAMIC);
        
        if (dynamicDomainOps.length === 0) {
            return false;
        }

        // For large operations, use batching approach
        return operations.length > config.migration.crossGsMigrationBatchSize;
    }

    private async processCrossGsOperations(entity: BaseEntity, operations: EntityBulkOperation[]): Promise<EntityBulkOperationResult[]> {
        log.info(`Processing ${operations.length} operations in batches of ${config.migration.crossGsMigrationBatchSize} for cross-GS migration`);
        
        const results: EntityBulkOperationResult[] = [];
        const batchSize = config.migration.crossGsMigrationBatchSize;
        
        // Process operations in batches to handle cross-GS migrations sequentially
        for (let i = 0; i < operations.length; i += batchSize) {
            const batch = operations.slice(i, i + batchSize);
            log.info(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(operations.length / batchSize)}: operations ${i + 1}-${Math.min(i + batchSize, operations.length)}`);
            
            try {
                const batchResults = await super.process(entity, batch);
                results.push(...batchResults);
                
                // Add a small delay between batches to avoid overwhelming migration service
                if (i + batchSize < operations.length) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            } catch (error) {
                log.error(error, `Failed to process batch ${Math.floor(i / batchSize) + 1}`);
                throw error;
            }
        }
        
        log.info(`Successfully processed all ${operations.length} operations in ${Math.ceil(operations.length / batchSize)} batches`);
        return results;
    }

    protected async validateOperations(entity: BaseEntity, operations: EntityBulkOperation[]): Promise<void> {
        await super.validateOperations(entity, operations);

        const errorMessages = [];
        for (const op of operations) {
            if (op.action === BULK_OPERATION_ACTION.SET && op.item.type === ENTITY_BULK_OPERATION_TYPE.STATIC) {
                const ent: any  = await entity.find({ key: op.entityKey });
                if (!ent.isMaster()) {
                    const parentSettings = await new EntitySettingsService(ent.getParent()).get();
                    if (parentSettings.allowedStaticDomainsForChildId) {
                        if (parentSettings.allowedStaticDomainsForChildId.indexOf(op.item.id) === -1) {
                            errorMessages.push(`Static domain cannot ${op.item.id} be applied to ${ent.path}, because parent's restriction`);
                        }
                    }
                }
            }
        }
        if (errorMessages.length) {
            throw new Errors.ValidationError(errorMessages);
        }
    }
}
