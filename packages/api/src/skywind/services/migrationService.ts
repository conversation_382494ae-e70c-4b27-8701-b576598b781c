import * as Errors from "../errors";
import * as superagent from "superagent";
import { sleep } from "@skywind-group/sw-utils";
import { generateInternalToken } from "../utils/token";
import logger from "../utils/logger";
import { BaseEntity, EntityWithChild, MIGRATION_STATUS } from "../entities/entity";
import { buildDynamicGSUrlByDomain } from "./entityDynamicDomainService";
import { Models } from "../models/models";
import { findOne } from "./entity";
import EntityCache from "../cache/entity";
import config from "../config";
import { getDynamicDomainService } from "./domain";
import { getGameServerApiProvider } from "./deploymentGroup";
import { BrandEntity } from "../entities/brand";
import { DynamicDomain } from "../entities/domain";
import { Transaction } from "sequelize";
import { measure, measureProvider } from "../utils/measures";

const log = logger("force-cleanup");
const FORCE_CLEANUP_ENDPOINT = "force-cleanup";
const FORCE_CLEANUP_PLAYER_ENDPOINT = "force-cleanup/interrupt-player";
const REACTIVATE_ENDPOINT = "force-cleanup/reactivate-game";

const EntityModel = Models.EntityModel;
const DynamicDomainModel = Models.DynamicDomainModel;

class MigrationService {

    @measure({ name: "MigrationService.startMigration" })
    public async startMigration(brandId: number,
                                prevDomains: DynamicDomain[],
                                transaction?: Transaction): Promise<void> {
        const callback = async () => {
            log.info("Starting force cleanup job for brand id = %s ", brandId);
            let attempts = config.migration.forceCleanupAttempts;
            do {
                attempts--;
                try {
                    for (const prevDomain of prevDomains) {
                        const url = await buildDynamicGSUrlByDomain(prevDomain, FORCE_CLEANUP_ENDPOINT);
                        await this.delete(url, { brandId });
                    }
                    await EntityModel.update(
                        {
                            migrationStatus: MIGRATION_STATUS.PROCESSING
                        },
                        {
                            where: {
                                id: brandId,
                                migrationStatus: MIGRATION_STATUS.STARTED
                            },
                            transaction
                        }
                    );
                    break;
                } catch (err) {
                    log.error(err, "Error when trying to send force cleanup for brand id %s", brandId);
                    if (attempts > 0) {
                        await sleep(1000);
                    } else {
                        return Promise.reject(err);
                    }
                }
            } while (attempts > 0);
        };
        if (measureProvider.getContext()) {
            await measureProvider.trackTransaction(callback)();
            return;
        }
        await measureProvider.runInTransaction("Merchant/brand migration", callback);
    }

    public checkStuckMigrations() {
        if (config.migration.checkStuckMigrationInternal) {
            setInterval(async () => this.doCheckMigrations(), config.migration.checkStuckMigrationInternal);
        }
    }

    public async doCheckMigrations(): Promise<void> {
        await measureProvider.runInTransaction("Checking migrations jobs", async () => {
            try {
                log.info("Checking migrations jobs");
                const master = await findOne({});
                const check = async (parent: BaseEntity | EntityWithChild) => {
                    if (parent.isBrand() && !!parent.prevDynamicDomainId &&
                        parent.migrationStatus !== MIGRATION_STATUS.PROCESSING) {
                        log.info("Found on-going migration for entity key %s", parent.key);
                        const dynamicDomain: DynamicDomain =
                            await DynamicDomainModel.findByPk(parent.prevDynamicDomainId);
                        setImmediate(() => this.startMigration(parent.id, [dynamicDomain]));
                    } else {
                        const entity = parent as EntityWithChild;
                        if (entity.child) {
                            await Promise.all(entity.child.map(item => check(item)));
                        }
                    }
                };
                await check(master);
            } catch (err) {
                log.error(err, "Error when trying to check stuck migrations");
            }
        });
    }

    public async markMigrationFinished(brandId: number): Promise<void> {
        const brand = await findOne({ id: brandId });
        if (!!brand.prevDynamicDomainId || (brand.inheritedDynamicDomainPoolId && brand.migrationStatus === MIGRATION_STATUS.PROCESSING)) {
            const [updatedRows] = await EntityModel.update({
                prevDynamicDomainId: null,
                migrationStatus: null
            } as any, { where: { id: brand.id, prevDynamicDomainId: brand.prevDynamicDomainId } });

            if (updatedRows !== 1) {
                return Promise.reject(new Errors.CannotMarkMigrationFinishedError());
            }

            brand.prevDynamicDomainId = undefined;
            EntityCache.reset();
        }
    }

    @measure({ name: "MigrationService.forceMigratePlayer" })
    public async forceMigratePlayer(brand: BaseEntity, playerCode: string): Promise<any> {
        if (brand.prevDynamicDomainId) {
            const domain: DynamicDomain = await getDynamicDomainService().findOne(brand.prevDynamicDomainId);
            const url = await buildDynamicGSUrlByDomain(domain, FORCE_CLEANUP_PLAYER_ENDPOINT);
            return this.delete(url, { brandId: brand.id, playerCode });
        }
    }

    @measure({ name: "MigrationService.reactivateGame" })
    public async reactivateGame(brand: BrandEntity, gameContextId: string): Promise<any> {
        const token = await generateInternalToken({ gameContextId });
        const keys = gameContextId.split(":");
        return getGameServerApiProvider().sendPostByEntity<void>(REACTIVATE_ENDPOINT, { token }, brand, keys[3]);
    }

    private async delete<T>(url: string, req): Promise<T> {
        log.info("Sending  to %s , request %j", url, req);
        const token = await generateInternalToken(req);
        return superagent
            .delete(url)
            .set("Content-Type", "application/json")
            .send({ token })
            .then((res) => this.processResponse(res))
            .catch((err) => this.processResponse(err.response, err));
    }

    private processResponse(response: superagent.Response, error?: Error){
        if (error && !response) {
            log.error(error, "Failed to query game server");
            return Promise.reject(new Errors.ErrorQueryingGameServer());
        }
        if (response.statusCode !== 200 && response.statusCode !== 201) {
            log.error({ body: response.body, statusCode: response.statusCode }, "Failed to send request to game server");
            return Promise.reject(new Errors.ErrorQueryingGameServer(response.body));
        }

        return response.body;
    }
}

export default new MigrationService();
