import config from "../config";
import Translator from "@skywind-group/sw-management-i18n";
import * as express from "express";
import { pipeline } from "node:stream/promises";
import { Server, createServer } from "node:http";
import { initCheckPromotionStartedJob } from "../services/promotions/playerPromotionService";
import { initPaymentStatusJob } from "../services/payment";
import {
    initFlatReportsJob,
    initLowBalanceNotificationsJob,
    initNotifiedFlatReportsJob,
    initReportingJobForPOP
} from "../jobs/jobStarter";
import { getVersion } from "../utils/version";
import { initDB, isMemoryMonitoring, isPrometheusMonitoring, setUpServerName } from "./common";
import { default as logger } from "../utils/logger";
import { measureProvider } from "../utils/measures";
import { initDomainMonitoringJob } from "../jobs/domainDetectorJob";

const compression = require("compression");
const bodyParser = require("body-parser");
const methodOverride = require("method-override");
const cookieParser = require("cookie-parser");

export default function create(setUp?: (app: express.Application) => void): express.Application {
    const app: express.Application = express();

    app.use(
        measureProvider.instrumentFunction(
            cookieParser(config.oAuth.cookieSecret),
            "express.cookieParser"
        ) as any
    );
    app.use(Translator.middleware);
    app.use(measureProvider.instrumentFunction(compression({
        threshold: config.compressionThreshold
    }), "express.compression") as any);
    app.use(measureProvider.instrumentFunction(bodyParser.urlencoded({
        extended: true, limit: config.bodyParserUrlLimit
    }), "express.urlencoded") as any);
    app.use(measureProvider.instrumentFunction(bodyParser.json({
        limit: config.bodyParserJsonLimit
    }), "express.jsonBodyParser") as any);

    app.use(methodOverride());
    app.disable("x-powered-by");

    if (setUp) {
        setUp(app);
    }

    return app;
}

export function setUpMetricHandlers(app: express.Application) {
    if (isPrometheusMonitoring()) {
        app.get("/metrics", async (req: express.Request, res: express.Response) => {
            measureProvider.setTransaction("Prometheus metrics");
            const metrics = await measureProvider.getMeasuresStream();
            await pipeline(metrics, res);
        });
    } else if (isMemoryMonitoring()) {
        app.use("/v1/", require("../api/measures").default);
    }
}

export async function startApplicationServer(application: express.Application,
                                             name: string,
                                             port: number,
                                             wrapper?: Function): Promise<Server> {
    if (config.promo.checkPromoStartedJobSchedule) {
        initCheckPromotionStartedJob(config.promo.checkPromoStartedJobSchedule);
    }
    if (config.paymentStatusJob.schedule) {
        initPaymentStatusJob();
    }

    if (config.flatReports.job.enabled) {
        initFlatReportsJob();
    }
    if (config.flatReports.notifiedJob.enabled) {
        initNotifiedFlatReportsJob();
    }

    if (config.reportPOPCriticalFilesJob.enabled) {
        await initReportingJobForPOP();
    }

    if (config.lowBalanceNotificationsJob.enabled) {
        await initLowBalanceNotificationsJob();
    }

    await initDomainMonitoringJob();

    const log = logger("start-up");
    setUpServerName(name);
    log.info("Server is starting");
    await initDB();
    const version = getVersion("/../");
    return new Promise<Server>((resolve) => {
        const server: Server = createServer(application);
        if (wrapper) {
            wrapper(server);
        }
        if (config.keepAliveTimeout) {
            server.keepAliveTimeout = config.keepAliveTimeout;
        }
        server.listen(port, null, () => {
            log.info(`${name} API listening on: ${port}`);
            log.info(`${name} API AppVersion: ${version}`);
            server.timeout = config.server.timeout;
            resolve(server);
        });
    });
}
