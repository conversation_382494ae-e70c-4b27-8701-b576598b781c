import { Cache } from "./cache";
import { Key } from "node-cache";

export class <PERSON>acheWithNulls<ID extends Key, T> extends Cache<ID, T> {

    public async find(id: ID, ...args): Promise<T> {
        if (this.cache && this.connected) {
            let value: T = this.cache.get(id);

            if (typeof value === "undefined") {
                value = await this.search.call(this.search, id, ...args);
                if (typeof value !== "undefined") {
                    this.cache.set(id, value);
                } else {
                    this.cache.del(id);
                }
            }

            return value;
        } else {
            return this.search.call(this.search, id, ...args);
        }
    }

}
