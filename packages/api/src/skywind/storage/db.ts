import { Options, PoolOptions, Transaction, ReplicationOptions, QueryOptions, Sequelize, Error } from "sequelize";
import config from "../config";
import { ReportingRequestCanceledByTimeoutError } from "../errors";
import { extendDateType } from "./dateWithoutTimeZone";
extendDateType();
const DB_DIALECT = "postgres";

function constructOptions(dbConfig): Options {
    const dbPool: PoolOptions = {

        /**
         * Maximum connections of the pool
         */
        max: dbConfig.maxConnections,

        /**
         * The maximum time, in milliseconds, that a connection can be idle before being released.
         */
        idle: dbConfig.maxIdleTime,

    };
    const dbOptions: Options = {

        /**
         * The dialect of the database you are connecting to. One of mysql, postgres, sqlite, mariadb and mssql.
         */
        dialect: DB_DIALECT,

        /**
         * Default isolation level
         */
        isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,

        /**
         * The dialect specific options
         */
        dialectOptions: {},

        /**
         * The host of the relational database.
         */
        host: dbConfig.host,

        /**
         * The port of the relational database.
         */
        port: dbConfig.port,

        /**
         * Connection pool options
         */
        pool: dbPool,

        /**
         * A function that gets executed everytime Sequelize would log something.
         *
         * Defaults to console.log
         */
        logging: config.queryLogging,

        define: {
            schema: dbConfig.schema,
        },
    };

    if (config.db.ssl.isEnabled) {
        dbOptions.dialectOptions["ssl"] = config.db.ssl;
    }

    return dbOptions;
}

// Build read replicas array - use new replica configs if available, otherwise fall back to slave config
const readReplicas = config.dbReplicas.length > 0 
    ? config.dbReplicas 
    : [{
        host: config.dbForReportingSlave.host,
        port: config.dbForReportingSlave.port,
        username: config.dbForReportingSlave.user,
        password: config.dbForReportingSlave.password,
        database: config.dbForReportingSlave.database
    }];

const replicationOptions: {query?: QueryOptions, replication?: ReplicationOptions} = {
    replication: {
        read: readReplicas,
        write: {
            host: config.db.host,
            port: config.db.port,
            username: config.db.user,
            password: config.db.password,
            database: config.db.database
        }
    }
};

export const sequelize = new Sequelize(
    config.db.database, null, null, {...constructOptions(config.db), ...replicationOptions }
);

export const sequelizeJpSlave = new Sequelize(
    config.dbReportingJpSlave.database,
    config.dbReportingJpSlave.user,
    config.dbReportingJpSlave.password,
    constructOptions(config.dbReportingJpSlave)
);

export const sequelizeWalletArchive =
    config.walletArchiveDb.host && new Sequelize(config.walletArchiveDb.database,
    config.walletArchiveDb.user,
    config.walletArchiveDb.password,
    constructOptions(config.walletArchiveDb));

export const gameServerDB = new Sequelize(
    config.gameServerDB.database,
    config.gameServerDB.user,
    config.gameServerDB.password,
    constructOptions(config.gameServerDB));

export const executeLongQuery = async <T>(db: Sequelize,
                                          callback: (trx: Transaction) => Promise<T>): Promise<T> => {
    try {
        return await db.transaction({ readOnly: true }, async (trx) => {
            await db.query(`SET LOCAL statement_timeout = ${config.dbForReportingSlave.maxQueryTimeout};`,
                { transaction: trx });
            return callback(trx);
        });
    } catch (err) {
        if (err instanceof Error && err.message === "canceling statement due to statement timeout") {
            return Promise.reject(new ReportingRequestCanceledByTimeoutError());
        }
        return Promise.reject(err);
    }
};
