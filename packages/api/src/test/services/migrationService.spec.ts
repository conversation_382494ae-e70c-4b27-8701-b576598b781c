import { suite, test, timeout } from "mocha-typescript";
import { SinonStub, stub } from "sinon";
import * as request from "request";
import * as superagent from "superagent";
import {
    complexStructure,
    createComplexStructure,
    flushAll,
    truncate,
    withTransactionDecorator
} from "../entities/helper";
import MigrationService from "../../skywind/services/migrationService";
import { getDynamicDomainService } from "../../skywind/services/domain";
import getEntityFactory from "../../skywind/services/entityFactory";
import { MIGRATION_STATUS } from "../../skywind/entities/entity";
import { Models } from "../../skywind/models/models";
import { expect, use } from "chai";
import config from "../../skywind/config";
import { verifyInternalToken } from "../../skywind/utils/token";
import { ErrorQueryingGameServer } from "../../skywind/errors";
import { sleep } from "@skywind-group/sw-utils";
import { BrandEntity } from "../../skywind/entities/brand";
import { DynamicDomain } from "../../skywind/entities/domain";
import * as DynamicDomainCache from "../../skywind/cache/dynamicDomainCache";

use(require("chai-as-promised"));

@suite()
export class MigrationServiceSpec {
    private deleteStub: SinonStub;
    private postStub: SinonStub;
    private static brand: BrandEntity;
    private static domain: DynamicDomain;
    private static newDomain: DynamicDomain;
    private static attempts: number;

    public static async before() {
        await flushAll();
        await truncate();
        const master = await createComplexStructure();
        this.domain = await getDynamicDomainService().create({
            domain: "gameserver.skywindgroup.com",
            environment: "gc"
        });
        this.newDomain = await getDynamicDomainService().create({
            domain: "new.gameserver.skywindgroup.com",
            environment: "gc1"
        });
        const entity = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key })).createEntity({
            name: "ENTITY",
            description: "ENTITY description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM"
        });
        this.brand = await getEntityFactory(entity).createBrand({
            name: "BRAND",
            type: "brand",
            description: "BRAND description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://website88.com"
        });
        this.brand.dynamicDomainId = this.newDomain.id;
        this.brand.prevDynamicDomainId = this.domain.id;
        this.brand.migrationStatus = MIGRATION_STATUS.STARTED;
        await Models.EntityModel.update({
            prevDynamicDomainId: this.domain.id,
            dynamicDomainId: this.newDomain.id,
            migrationStatus: MIGRATION_STATUS.STARTED
        }, { where: { id: this.brand.id } });
        this.attempts = config.migration.forceCleanupAttempts;
        config.migration.forceCleanupAttempts = 1;
    }

    public static after() {
        config.migration.forceCleanupAttempts = this.attempts;
    }

    public before() {
        this.deleteStub = stub(superagent, "delete");
        this.postStub = stub(request, "post");
    }

    public after() {
        this.deleteStub.restore();
        this.postStub.restore();
    }

    @withTransactionDecorator()
    @test()
    public async testStartMigrationSuccessfully() {
        this.deleteStub.returns({
            set: () => ({
                send: async (data?: any) => {
                    expect(await verifyInternalToken(data.token))
                        .contains({ brandId: MigrationServiceSpec.brand.id });

                    return Promise.resolve({
                        statusCode: 200,
                        body: undefined
                    })
                }
            })
        });

        await MigrationService.startMigration(MigrationServiceSpec.brand.id, [MigrationServiceSpec.domain]);

        expect(this.deleteStub.args[0][0]).equal("https://gameserver.skywindgroup.com:4000/force-cleanup");

        const brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).equals(MigrationServiceSpec.domain.id);
        expect(brandAttributes.migrationStatus).equals(MIGRATION_STATUS.PROCESSING);
    }

    @withTransactionDecorator()
    @test()
    public async testSkipChangingMigrationStatusIfItWasFinished() {
        this.deleteStub.returns({
            set: () => ({
                send: async (data?: any) => {
                    expect(await verifyInternalToken(data.token))
                        .contains({ brandId: MigrationServiceSpec.brand.id });

                    return {
                        statusCode: 200,
                        body: undefined
                    };
                }
            })
        });

        await MigrationService.startMigration(MigrationServiceSpec.brand.id, [MigrationServiceSpec.domain]);

        expect(this.deleteStub.args[0][0]).equal("https://gameserver.skywindgroup.com:4000/force-cleanup");

        let brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).equals(MigrationServiceSpec.domain.id);
        expect(brandAttributes.migrationStatus).equals(MIGRATION_STATUS.PROCESSING);
        await MigrationService.markMigrationFinished(MigrationServiceSpec.brand.id);
        brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).is.null;
        expect(brandAttributes.migrationStatus).is.null;

        await MigrationService.startMigration(MigrationServiceSpec.brand.id, [MigrationServiceSpec.domain]);
        expect(this.deleteStub.args[0][0]).equal("https://gameserver.skywindgroup.com:4000/force-cleanup");

        brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).is.null;
        expect(brandAttributes.migrationStatus).is.null;

        await MigrationService.markMigrationFinished(MigrationServiceSpec.brand.id);
        brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).is.null;
        expect(brandAttributes.migrationStatus).is.null;
    }

    @withTransactionDecorator()
    @test()
    public async testStartMigrationFailed() {
        this.deleteStub.returns({
            set: () => ({
                send: async (data?: any) => {
                    expect(await verifyInternalToken(data.token))
                        .contains({ brandId: MigrationServiceSpec.brand.id });

                    return {
                        statusCode: 500,
                        body: undefined
                    };
                }
            })
        });

        await expect(MigrationService.startMigration(MigrationServiceSpec.brand.id, [MigrationServiceSpec.domain]))
            .to.be.rejectedWith(ErrorQueryingGameServer);

        expect(this.deleteStub.args[0][0]).equal("https://gameserver.skywindgroup.com:4000/force-cleanup");

        const brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).equals(MigrationServiceSpec.domain.id);
        expect(brandAttributes.migrationStatus).equals(MIGRATION_STATUS.STARTED);
    }

    @withTransactionDecorator()
    @test()
    public async testMarkMigrationFinishedSuccessful() {
        await MigrationService.markMigrationFinished(MigrationServiceSpec.brand.id);

        const brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).is.null;
        expect(brandAttributes.migrationStatus).is.null;
    }

    @test()
    public async testForceMigratePlayer() {
        this.deleteStub.returns({
            set: () => ({
                send: async (data?: any) => {
                    expect(await verifyInternalToken(data.token))
                        .contains({ brandId: MigrationServiceSpec.brand.id, playerCode: "player001" });

                    return {
                        statusCode: 200,
                        body: undefined
                    };
                }
            })
        });
        await MigrationService.forceMigratePlayer(MigrationServiceSpec.brand, "player001");
        expect(this.deleteStub.args[0][0])
            .equal("https://gameserver.skywindgroup.com:4000/force-cleanup/interrupt-player");
    }

    @test()
    public async testForceMigratePlayerFailed() {
        this.deleteStub.returns({
            set: () => ({
                send: async (data?: any) => {
                    expect(await verifyInternalToken(data.token))
                        .contains({ brandId: MigrationServiceSpec.brand.id, playerCode: "player001" });

                    return {
                        statusCode: 500,
                        body: undefined
                    };
                }
            })
        });
        await expect(MigrationService.forceMigratePlayer(MigrationServiceSpec.brand, "player001"))
            .to.be.rejectedWith(ErrorQueryingGameServer);
        expect(this.deleteStub.args[0][0])
            .equal("https://gameserver.skywindgroup.com:4000/force-cleanup/interrupt-player");
    }

    @withTransactionDecorator()
    @test()
    @timeout(3000)
    public async testDoCheckMigration() {
        this.deleteStub.returns({
            set: () => ({
                send: async (data?: any) => {
                    expect(await verifyInternalToken(data.token))
                        .contains({ brandId: MigrationServiceSpec.brand.id });

                    return {
                        statusCode: 200,
                        body: undefined
                    };
                }
            })
        });

        await MigrationService.doCheckMigrations();
        await sleep(500);

        expect(this.deleteStub.args[0][0]).equal("https://gameserver.skywindgroup.com:4000/force-cleanup");

        const brandAttributes = await Models.EntityModel
            .findByPk(MigrationServiceSpec.brand.id).then(item => item.toJSON());
        expect(brandAttributes.dynamicDomainId).equals(MigrationServiceSpec.newDomain.id);
        expect(brandAttributes.prevDynamicDomainId).equals(MigrationServiceSpec.domain.id);
        expect(brandAttributes.migrationStatus).equals(MIGRATION_STATUS.PROCESSING);
    }

    @test()
    public async testReactivateGame() {
        DynamicDomainCache.reset();
        this.postStub.yields(null, { statusCode: 200 }, undefined);
        await MigrationService.reactivateGame(MigrationServiceSpec.brand, "some_game_context_id");
        expect(this.postStub.args[0][0])
            .equal("https://new.gameserver.skywindgroup.com:4000/force-cleanup/reactivate-game");
        expect(await verifyInternalToken(this.postStub.args[0][1].body.token))
            .contains({ gameContextId: "some_game_context_id" });
    }
}
