import { suite, test, timeout } from "mocha-typescript";
import { expect, should, use } from "chai";
import { truncate } from "../entities/helper";
import { BulkService } from "../../skywind/services/bulk/bulkService";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import {
    BULK_OPERATION_ACTION,
    ENTITY_BULK_OPERATION_TYPE,
    EntityBulkOperation, EntityBulkOperationResult
} from "../../skywind/entities/bulk";
import { ValidationError } from "../../skywind/errors";
import { EntityBulkService } from "../../skywind/services/bulk/entityBulkService";
import { SinonSpy, spy } from "sinon";
import { ApplicationLock } from "../../skywind/utils/applicationLock";

import { sequelize as db } from "../../skywind/storage/db";
import { QueryTypes } from "sequelize";

const chaiAsPromised = require("chai-as-promised");

@suite(timeout(20000))
class BulkServiceSpec {
    public service: BulkService<EntityBulkOperation, EntityBulkOperationResult> = new EntityBulkService();
    public applicationLockSpy: SinonSpy;

    public static async before() {
        should();
        use(chaiAsPromised);
        await truncate();
    }

    public before() {
        this.applicationLockSpy = spy(ApplicationLock, "lock");
    }

    public after() {
        this.applicationLockSpy.restore();
    }

    @test()
    public async process() {
        const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN);
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN);
        const proxy = await factory.create(FACTORY.PROXY);

        const parent = await factory.create(FACTORY.ENTITY, {},
            { dynamicDomainId: dynamicDomain.id, environment: dynamicDomain.environment });

        const entity = await factory.create(FACTORY.ENTITY, {}, { parent });
        const brand = await factory.create(FACTORY.BRAND, {}, { staticDomainId: staticDomain.id, parent });

        const merchant = await factory.create(FACTORY.MERCHANT_ENTITY, {}, { parent });
        await factory.create(FACTORY.MERCHANT, {}, {
            brandId: merchant.id
        });

        const setDynamicDomainToEntity = {
            entityKey: entity.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC,
                id: dynamicDomain.id
            }
        };

        const resetStaticDomainFromBrand = {
            entityKey: brand.key,
            action: BULK_OPERATION_ACTION.RESET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.STATIC,
            }
        };

        const setProxyToMerchant = {
            entityKey: merchant.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.PROXY,
                id: proxy.id
            }
        };

        const operations: EntityBulkOperation[] = [
            setDynamicDomainToEntity,
            resetStaticDomainFromBrand,
            setProxyToMerchant
        ];

        const results = await this.service.process(parent, operations);

        expect(results.length).to.be.equal(3);

        expect(this.applicationLockSpy.calledOnce);
    }

    @test()
    public async processTwoOperationForOneEntity() {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN);
        const proxy = await factory.create(FACTORY.PROXY);

        const parent = await factory.create(FACTORY.ENTITY, {},
            { dynamicDomainId: dynamicDomain.id, environment: dynamicDomain.environment });

        const merchant = await factory.create(FACTORY.MERCHANT_ENTITY, {}, { parent });
        await factory.create(FACTORY.MERCHANT, {}, {
            brandId: merchant.id
        });

        const setDynamicDomainToEntity = {
            entityKey: merchant.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC,
                id: dynamicDomain.id
            }
        };
        const setProxyToMerchant = {
            entityKey: merchant.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.PROXY,
                id: proxy.id
            }
        };

        const operations: EntityBulkOperation[] = [
            setDynamicDomainToEntity,
            setProxyToMerchant
        ];

        const results = await this.service.process(parent, operations);

        expect(results.length).to.be.equal(2);
    }

    @test()
    public async processSetOperationWithoutItemId() {
        const parent = await factory.create(FACTORY.ENTITY);
        const entity = await factory.create(FACTORY.ENTITY, {}, { parent });
        const setWithItemID = {
            entityKey: entity.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.PROXY
            }
        };
        await this.service.process(parent, [ setWithItemID ])
            .should.eventually.rejectedWith(ValidationError);
    }

    @test()
    public async processResetWithWrongEntityKey() {
        const parent = await factory.create(FACTORY.ENTITY);
        const resetWithWrongEntityKey = {
            entityKey: "cheburecKEY",
            action: BULK_OPERATION_ACTION.RESET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.PROXY,
                id: 1
            }
        };
        await this.service.process(parent, [ resetWithWrongEntityKey ])
            .should.eventually.rejectedWith(ValidationError);
    }

    @test()
    public async processWithoutAction() {
        const parent = await factory.create(FACTORY.ENTITY);
        const entity = await factory.create(FACTORY.ENTITY, {}, { parent });
        const withoutAction = {
            entityKey: entity.key,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC,
                id: 1
            }
        };
        await this.service.process(parent, [ withoutAction as any ])
            .should.eventually.rejectedWith(ValidationError);
    }

    @test()
    public async processInvalidDynamicID() {
        const parent = await factory.create(FACTORY.ENTITY);
        const entity = await factory.create(FACTORY.ENTITY, {}, { parent });
        const setInvalidDomain = {
            entityKey: entity.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC,
                id: 1231231
            }
        };
        await this.service.process(parent, [ setInvalidDomain ])
            .should.eventually.rejectedWith(ValidationError);
    }

    @test()
    public async processInvalidProxyID() {
        const parent = await factory.create(FACTORY.ENTITY);
        const merchant = await factory.create(FACTORY.MERCHANT_ENTITY, {}, { parent });
        await factory.create(FACTORY.MERCHANT, {}, {
            brandId: merchant.id
        });
        const setInvalidProxy = {
            entityKey: merchant.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.PROXY,
                id: 1231231
            }
        };
        await this.service.process(parent, [ setInvalidProxy ])
            .should.eventually.rejectedWith(ValidationError);
    }

    @test()
    public async processSetProxyToNonMerchant() {
        const parent = await factory.create(FACTORY.ENTITY);
        const entity = await factory.create(FACTORY.ENTITY, {}, { parent });

        const setProxyToNonMerchant = {
            entityKey: entity.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.PROXY,
                id: 1231231
            }
        };
        await this.service.process(parent, [ setProxyToNonMerchant ])
            .should.eventually.rejectedWith(ValidationError);
    }

    @test()
    public async setDynamicDomainShouldTriggerMigrationForCrossGsChange() {
        const [ firstDomain, secondDomain ] = await factory.createMany(FACTORY.DYNAMIC_DOMAIN, 2, { environment: "env1" }, { environment: "env2" });

        const parent = await factory.create(FACTORY.ENTITY, {},
            { dynamicDomainId: firstDomain.id, environment: firstDomain.environment });

        const entity = await factory.create(FACTORY.ENTITY, {}, { parent });

        const setDynamicDomain = {
            entityKey: entity.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC,
                id: secondDomain.id
            }
        };
        
        // Should successfully process and trigger migration instead of throwing error
        const results = await this.service.process(parent, [ setDynamicDomain ]);
        expect(results).to.have.length(1);
        // The result should be the entity itself, indicating successful processing
        expect(results[0]).to.exist;
    }

    @test()
    public async setProxyShouldBeWithoutLockDB() {
        const parent = await factory.create(FACTORY.ENTITY);

        const merchant = await factory.create(FACTORY.MERCHANT_ENTITY, {}, { parent });
        await factory.create(FACTORY.MERCHANT, {}, {
            brandId: merchant.id
        });

        const proxy = await factory.create(FACTORY.PROXY);

        const setProxyToMerchant = {
            entityKey: merchant.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.PROXY,
                id: proxy.id
            }
        };

        const results = await this.service.process(parent, [ setProxyToMerchant ]);
        expect(results.length).to.be.equal(1);

        expect(this.applicationLockSpy.notCalled);
    }

    @test()
    public async setStaticDomainShouldBeRejectedIfDomainNotExistsInTags() {
        const parent = await factory.create(FACTORY.ENTITY);
        const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN);
        const staticDomainTags = ["chebureck-egor.com"];
        const brand = await factory.create(FACTORY.BRAND, {}, { parent, staticDomainTags });

        const setStaticDomain = {
            entityKey: brand.key,
            action: BULK_OPERATION_ACTION.SET,
            item: {
                type: ENTITY_BULK_OPERATION_TYPE.STATIC,
                id: staticDomain.id
            }
        };

        await this.service.process(parent, [ setStaticDomain ])
            .should.eventually.rejectedWith(ValidationError,
                `Validation error: Domain is not valid. Allowed tags - ${staticDomainTags}`);
    }

    @test()
    // this test is created to reproduce the SWS-33384, where incorrect usage of Postgres Session locks could cause
    // db trx to get stuck and occupy its connection. So in this test we firstly reproduce the 'stuck' trx state
    // and then proceed to recover it
    public async checkPostgresSessionLevelLockNegativeScenario() {

        await db.transaction(async (transaction) => {

            // obtain session-level db lock
            await db.query("select pg_advisory_lock(100)", { type: QueryTypes.SELECT, transaction, raw: true });

            try {
                await db.query("select 1/0;", { type: QueryTypes.SELECT, transaction, raw: true })
                    .should.eventually.rejectedWith("division by zero")
                    .and.be.an.instanceOf(Error);
            } finally {
                    // try unlock while in transaction - it must fail
                    await db.query("select pg_advisory_unlock(100)",
                        { type: QueryTypes.SELECT, transaction, raw: true })
                        .should.eventually.rejectedWith("current transaction is aborted, commands ignored until end of transaction block")
                        .and.be.an.instanceOf(Error);

                    // now do rollback and then unlock MUST be succesful
                    await db.query("rollback", { type: QueryTypes.SELECT, raw: true })
                        .should.eventually.not.be.rejected;
                    await db.query("select pg_advisory_unlock(100)",
                        { type: QueryTypes.SELECT, transaction, raw: true }).should.eventually.not.be.rejected;
            }
        });
    }

    @test()
    public async checkPostgresTransactionLevelLockPositiveScenario() {
        await db.transaction(async (transaction) => {
            // obtain trx-level db lock
            await ApplicationLock.lock(transaction, 101 as any);
            await db.query("select 1/0;", { type: QueryTypes.SELECT, transaction, raw: true })
                .should.eventually.rejectedWith("division by zero")
                .and.be.an.instanceOf(Error);
        });

        // try to obtain lock after previous failed trx that was holding a lock
        await db.transaction(async (transaction) => {
            // obtain trx-level db lock
            await ApplicationLock.lock(transaction, 101 as any).should.eventually.not.be.rejected;
        });
    }
}
