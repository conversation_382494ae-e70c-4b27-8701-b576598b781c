{"name": "@skywind-group/sw-management-gameprovider-api", "private": true, "version": "2.141.0-develop", "release": "5.57", "description": "", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "index.js", "scripts": {"clean": "rm -rf ./out", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p out/skywind && echo $(node -p \"require('./package.json').release\") $( git log --pretty=format:'%h' -n 1) $(date) > ./out/skywind/version", "only-test": "MEASURES_BASE_INSTRUMENT=false nyc mocha out/test/**/**/*.spec.js out/test/**/*.spec.js out/test/*.spec.js", "dev": "nodemon -r dotenv/config src/skywind/appGameProvider.ts"}, "dependencies": {"@fastify/compress": "8.0.1", "@fastify/cookie": "11.0.2", "@fastify/middie": "9.0.3", "@fastify/static": "8.1.1", "@skywind-group/gelf-stream": "1.2.6", "@skywind-group/sw-adapter-regulation-support": "^1.0.2", "@skywind-group/sw-currency-exchange": "2.3.19", "@skywind-group/sw-deferred-payment-cache": "^2.0.0", "@skywind-group/sw-management-deferredpayment": "workspace:~2.141.0-develop", "@skywind-group/sw-management-gameprovider": "workspace:~2.141.0-develop", "@skywind-group/sw-management-gameprovider-core": "workspace:~2.141.0-develop", "@skywind-group/sw-management-i18n": "workspace:~2.141.0-develop", "@skywind-group/sw-management-playersession": "workspace:~2.141.0-develop", "@skywind-group/sw-management-playservice": "workspace:~2.141.0-develop", "@skywind-group/sw-management-wallet": "workspace:~2.141.0-develop", "@skywind-group/sw-messaging": "0.2.4", "@skywind-group/sw-utils": "2.5.3", "@skywind-group/sw-wallet": "1.0.8", "@skywind-group/sw-wallet-adapter-core": "2.1.9", "agentkeepalive": "^4.5.0", "bole": "5.0.15", "bole-console": "0.1.10", "cls-hooked": "4.2.2", "emitter-listener": "1.1.2", "express-prom-bundle": "7.0.2", "fast-xml-parser": "4.4.1", "fastify": "5.3.0", "generic-pool": "3.9.0", "hashids": "^2.3.0", "inversify": "5.0.1", "inversify-express-utils": "6.3.2", "inversify-inject-decorators": "3.1.0", "ioredis": "5.5.0", "js-big-integer": "1.0.2", "kafka-node": "5.0.0", "method-override": "3.0.0", "pg": "8.14.1", "prom-client": "15.0.0", "reflect-metadata": "0.2.2", "uuid": "^9.0.1", "sequelize": "6.37.7", "superagent": "10.2.3", "superagent-proxy": "3.0.0"}, "devDependencies": {"@types/chai": "^4.3.11", "@types/chai-as-promised": "^7.1.8", "@types/chai-datetime": "1.0.0", "@types/cls-hooked": "^4.2.0", "@types/i18n": "0.13.12", "@types/mocha": "^10.0.8", "@types/node": "22.14.1", "@types/pg": "8.11.11", "@types/serve-static": "1.15.7", "@types/sinon": "10.0.20", "@types/sinon-chai": "3.2.12", "chai": "~4.3.10", "chai-as-promised": "^7.1.1", "chai-datetime": "1.8.1", "mocha": "10.7.3", "mocha-typescript": "1.1.12", "nyc": "15.1.0", "sinon": "16.1.3", "sinon-chai": "3.7.0", "ts-node": "^10.7.0", "tsconfig-paths": "^4.2.0", "typescript": "5.6.3"}, "packageManager": "pnpm@10.15.0"}