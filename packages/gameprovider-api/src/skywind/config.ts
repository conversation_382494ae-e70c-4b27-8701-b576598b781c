// tslint:disable:max-line-length
import { logging } from "@skywind-group/sw-utils";
import { splitEnvParameters } from "./utils/envParse";

let serverName: string = process.env.SERVER_NAME;

function logQueries(sql: string, timing?: number) {
    logging.logger("sequelize").info(sql);
}

const config = {

    environment: process.env.NODE_ENV || "development",

    isProduction: (): boolean => {
        return config.environment === "production";
    },

    region: process.env.REGION_TYPE || "default",

    server: {
        timeout: +process.env.SERVER_TIMEOUT || 120000,
        setName: (name: string) => {
            serverName = name;
        },
        getName: (): string => {
            return serverName;
        }
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4004,
        api: {
            isEnabled: process.env.INTERNAL_API === "true",
        },
    },

    db: {
        database: process.env.PGDATABASE || "management",
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.PGSCHEMA || "public",
        syncOnStart: process.env.SYNC_ON_START === "true"
    },

    walletArchiveDb: {
        database: process.env.MAPI_ARCHIVE_PGDATABASE,
        user: process.env.MAPI_ARCHIVE_PGUSER,
        password: process.env.MAPI_ARCHIVE_PGPASSWORD,
        host: process.env.MAPI_ARCHIVE_PGHOST,
        port: +process.env.MAPI_ARCHIVE_PGPORT,
        ssl: {
            isEnabled: process.env.MAPI_ARCHIVE_PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.MAPI_ARCHIVE_PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.MAPI_ARCHIVE_PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.MAPI_ARCHIVE_PGSCHEMA || "public",
    },

    redis: {
        host: process.env.MANAGEMENT_REDIS_HOST || process.env.REDIS_HOST || "redis",
        port: +process.env.MANAGEMENT_REDIS_PORT || +process.env.REDIS_PORT || 6379,
        password: process.env.MANAGEMENT_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        connectionTimeout: +process.env.MANAGEMENT_REDIS_CONNECTION_TIMEOUT || +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.MANAGEMENT_REDIS_SENTINELS || process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.MANAGEMENT_REDIS_SENTINEL_USERNAME || process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.MANAGEMENT_REDIS_SENTINEL_PASSWORD || process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.MANAGEMENT_REDIS_CLUSTER_NAME || process.env.REDIS_CLUSTER_NAME || "redis-ha",
        minConnections: +process.env.MANAGEMENT_REDIS_MIN_CONNECTIONS || +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.MANAGEMENT_REDIS_MAX_CONNECTIONS || +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.MANAGEMENT_REDIS_MAX_IDLE_TIME_MS || +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        replicationFactor: +process.env.MANAGEMENT_REDIS_REPLICATION_FACTOR || +process.env.REDIS_REPLICATION_FACTOR || 0,
        replicationTimeout: +process.env.MANAGEMENT_REDIS_REPLICATION_TIMEOUT || +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.MANAGEMENT_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.MANAGEMENT_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    walletRedis: {
        host: process.env.WALLET_REDIS_HOST || process.env.REDIS_HOST || "redis",
        port: +process.env.WALLET_REDIS_PORT || +process.env.REDIS_PORT || 6379,
        password: process.env.WALLET_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        connectionTimeout: +process.env.WALLET_REDIS_CONNECTION_TIMEOUT || +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.WALLET_REDIS_SENTINELS || process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.WALLET_REDIS_SENTINEL_USERNAME || process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.WALLET_REDIS_SENTINEL_PASSWORD || process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.WALLET_REDIS_CLUSTER_NAME || process.env.REDIS_CLUSTER_NAME || "redis-ha",
        minConnections: +process.env.WALLET_REDIS_MIN_CONNECTIONS || +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.WALLET_REDIS_MAX_CONNECTIONS || +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.WALLET_REDIS_MAX_IDLE_TIME_MS || +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        replicationFactor: +process.env.WALLET_REDIS_REPLICATION_FACTOR || +process.env.REDIS_REPLICATION_FACTOR || 0,
        replicationTimeout: +process.env.WALLET_REDIS_REPLICATION_TIMEOUT || +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.WALLET_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.WALLET_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" ||
            process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    playerSessionRedis: {
        host: process.env.PLAYER_SESSION_REDIS_HOST || process.env.MANAGEMENT_REDIS_HOST || process.env.REDIS_HOST || "redis",
        port: +process.env.PLAYER_SESSION_REDIS_PORT || +process.env.MANAGEMENT_REDIS_PORT || +process.env.REDIS_PORT || 6379,
        password: process.env.PLAYER_SESSION_REDIS_PASSWORD || process.env.MANAGEMENT_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        connectionTimeout: +process.env.PLAYER_SESSION_REDIS_CONNECTION_TIMEOUT || +process.env.MANAGEMENT_REDIS_CONNECTION_TIMEOUT || +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.PLAYER_SESSION_REDIS_SENTINELS || process.env.MANAGEMENT_REDIS_SENTINELS || process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.PLAYER_SESSION_REDIS_SENTINEL_USERNAME || process.env.MANAGEMENT_REDIS_SENTINEL_USERNAME || process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.PLAYER_SESSION_REDIS_SENTINEL_PASSWORD || process.env.MANAGEMENT_REDIS_SENTINEL_PASSWORD || process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.PLAYER_SESSION_REDIS_CLUSTER_NAME || process.env.MANAGEMENT_REDIS_CLUSTER_NAME || process.env.REDIS_CLUSTER_NAME || "redis-ha",
        minConnections: +process.env.PLAYER_SESSION_REDIS_MIN_CONNECTIONS || +process.env.MANAGEMENT_REDIS_MIN_CONNECTIONS || +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.PLAYER_SESSION_REDIS_MAX_CONNECTIONS || +process.env.MANAGEMENT_REDIS_MAX_CONNECTIONS || +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PLAYER_SESSION_REDIS_MAX_IDLE_TIME_MS || +process.env.MANAGEMENT_REDIS_MAX_IDLE_TIME_MS || +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        replicationFactor: +(process.env.PLAYER_SESSION_REDIS_REPLICATION_FACTOR ?? process.env.MANAGEMENT_REDIS_REPLICATION_FACTOR ?? process.env.REDIS_REPLICATION_FACTOR ?? 0),
        replicationTimeout: +process.env.PLAYER_SESSION_REDIS_REPLICATION_TIMEOUT || +process.env.MANAGEMENT_REDIS_REPLICATION_TIMEOUT || +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.PLAYER_SESSION_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.MANAGEMENT_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.PLAYER_SESSION_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.MANAGEMENT_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    startGameToken: {
        algorithm: process.env.START_GAME_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.START_GAME_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.START_GAME_TOKEN_EXPIRES_IN || 7200, // 2 hours
        secret: process.env.START_GAME_TOKEN_SECRET ||
            "yLQDmzHLqmQrjp6Z8KvuwcTwQe7TM5qMdCj4w8k8AFBXkHHDEndhUdevTw6QYPKp",
    },

    internalServerToken: {
        expiresIn: +process.env.INTERNAL_SERVER_TOKEN_EXPIRES_IN || 300,
        algorithm: process.env.INTERNAL_SERVER_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.INTERNAL_SERVER_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.INTERNAL_SERVER_TOKEN_SECRET || "TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
    },

    gameToken: {
        algorithm: process.env.GAME_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.GAME_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.GAME_TOKEN_SECRET || "PjvMCu7AmUuhNTzYFqLHrYTctKxUQEpcygDJ7qePxjhWDahCQ2PqSynf93wt8ndW",
    },
    skipGameTokenIssuerValidation: process.env.SKIP_GAME_TOKEN_ISSUER_VALIDATION === "true",

    logLevel: process.env.LOG_LEVEL || "info",

    graylog: {
        host: process.env.GRAYLOG_HOST || undefined,
        port: +process.env.GRAYLOG_PORT || undefined,
    },

    logParams: {
        secureSalt: "**********!#$*&^%)(",
        secureKeys: [
            "password",
            "newPassword",
            "key",
            "token",
            "accessToken",
            "secretKey",
            "secret",
            "merch_pwd",
            "confirmPassword",
            "privateToken",
            "privateKey"
        ],
        skipBodyOnUrl: ["/gameprovider/v1/play/"],
        keylen: 40,
        encryptIters: 48,
    },

    // logging all POSTGRES queries to console
    queryLogging: process.env.POSTGRES_QUERY_LOGGING === "true" ? logQueries : (sql: string, timing?: number) => {
    }, // tslint:disable-line

    bodyParserJsonLimit: +process.env.BODY_PARSER_JSON_LIMIT || 5242880,
    bodyParserUrlLimit: +process.env.BODY_PARSER_URL_LIMIT || 5242880,
    compressionThreshold: +process.env.COMPRESSION_THRESHOLD || 1024,

    keepAliveTimeout: +process.env.KEEP_ALIVE_TIMEOUT || 0,

    extGameproviderHistory: {
        on: process.env.EXT_BET_WIN_HISTORY_ENABLE !== "false",
        awaitHistorySave: process.env.AWAIT_EXT_HISTORY_SAVE !== "false"
    },

    deferredPayment: {
        on: process.env.DEFERRED_PAYMENT_ENABLE === "true",
        url: process.env.DEFERRED_PAYMENT_API_URL || "http://deferred-payment-api:3910",
        keepAlive: {
            maxFreeSockets: +(process.env.DEFERRRED_PAYMENT_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.DEFERRRED_PAYMENT_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.DEFERRRED_PAYMENT_SOCKET_ACTIVE_TTL || 60000
        },
        retries: {
            sleep: +(process.env.DEFERRRED_PAYMENT_RETRIES_SLEEP || 0),
            maxTimeout: +process.env.DEFERRRED_PAYMENT_RETRIES_MAX_TIMEOUT || 400,
        },
        requestTimeout: +process.env.DEFERRRED_PAYMENT_REQUEST_TIMEOUT || 200,
    },

    nats: {
        servers: JSON.parse(process.env.NATS_SERVERS || `["${process.env.NATS_URL || "nats://nats:4222"}"]`)
    },

    notificationType: process.env.GAME_PROVIDER_API_NOTIFICATION_TYPE || "redis",

    walletExternalLog: {
        on: process.env.EXTERNAL_TRX_ON === "true",
        config: {
            kafkaBrokerHostnames: process.env.EXTERNAL_TRX_KAFKA_BROKERS || "kafka:9092",
            topicName: process.env.EXTERNAL_TRX_TOPIC_NAME || "wallet_operation_log",
            publish: {
                ackTimeoutMs: +process.env.EXTERNAL_TRX_ACK_TIMEOUT || 1000,
                clientCreationTimeout: +process.env.EXTERNAL_CLIENT_CREATION_TIMEOUT || 6000,
                requestTimeout: +process.env.EXTERNAL_TRX_REQUEST_TIMEOUT || 1000,
                maxSendAttemptTimeout: +process.env.EXTERNAL_TRX_MAX_ATTEMPT_TIMEOUT || 10000
            }
        }
    },

    gameAuthAPI: {
        url: process.env.GAME_AUTH_API_URL || "http://game-auth-api:3007",
        keepAlive: {
            maxFreeSockets: +(process.env.GAME_AUTH_API_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.GAME_AUTH_API_KEEPT_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.GAME_AUTH_API_KEEP_SOCKET_ACTIVE_TTL || 60000
        }
    },

    operatorInfoRepository: {
        channelPrefix: process.env.OPERATOR_INFO_NOTIFICATION_CHANNEL || "operator-info",
        cacheChannelPrefix: process.env.OPERATOR_INFO_CAHE_NOTIFICATION_CHANNEL || "operator-info-cache",
        cacheTTL: +process.env.OPERATOR_INFO_CACHE_TTL || 60 * 5 * 1000,
        checkInterval: +process.env.OPERATOR_INFO_CACHE_CHECK_INTERVAL || 60 * 4 * 1000,
    },

    walletConductor: {
        type: process.env.WALLET_CONDUCTOR_TYPE || "direct",
        baseURL: process.env.WALLET_HTTP_CONDUCTOR_URL || "http://localhost:6000/"
    },

    trxIdRange: process.env.WALLET_TRX_ID_MIN || process.env.WALLET_TRX_ID_MAX ? {
        min: process.env.WALLET_TRX_ID_MIN,
        max: process.env.WALLET_TRX_ID_MAX,
    } : undefined,

    loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || process.env.GRAYLOG_HOST && "graylog" || "console") as any,

    playV1ExcludedSettings: JSON.parse(process.env.PLAYV1_EXCLUDED_SETTINGS || "[\"splitPayment\", \"autoCreateTestJackpot\", \"transferEnabled\", \"contributionPrecision\", \"jpTickerRefreshPeriod\", \"deferredContribution\", \"logoutControl\", \"hideBalanceBeforeAndAfter\"]"),

    disableSaveCanceledRound: process.env.DISABLE_SAVE_CANCELED_ROUND === "true",
    allowedHTTPMethods: splitEnvParameters(
        process.env.ALLOWED_HTTP_METHODS,
        ["GET", "POST", "OPTIONS", "DELETE", "PATCH", "PUT", "HEAD"]
    ),
};

export default config;
