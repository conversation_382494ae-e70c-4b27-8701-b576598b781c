import {
    BonusCoinsAccountFilter,
    BonusCoinsBalance,
    FreeBetsAccountFilter,
    FreeBetsBalance,
    PlayerBonusCoinWallet,
    PlayerFreebetWallet,
    PlayerGameFreebet,
    PromoWalletErrors
} from "@skywind-group/sw-management-promo-wallet";
import {
    BAD_TRANSACTION_ID,
    IAccount,
    INSUFFICIENT_BALANCE,
    ITransaction,
    IWallet,
    TRANSACTION_EXISTS
} from "@skywind-group/sw-wallet";
import {
    AccountPropertiesFilter,
    ONLY_BALANCE_FILTER,
    OPERATION_ID,
    PLAYER,
    WalletErrors,
    WalletFacade
} from "@skywind-group/sw-management-wallet";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import {
    FreeBetInfo,
    FreeBetInfoRequest,
    MerchantGameTokenData,
    OfflineBonusInfo,
    OfflineBonusPaymentRequest,
    PaymentRequest,
    RegisterRoundResponse
} from "@skywind-group/sw-wallet-adapter-core";
import { MerchantAdapterHelper } from "./merchantAdapterHelper";
import { Balance, Balances } from "./playService";
import { WALLET_OPERATION_NAME } from "./constant";
import { RegisterRoundRequest } from "./request";
import config, { PROMO_LOCATION } from "./config";

export class MerchantPlayer {

    constructor(private merchant: MerchantAdapterHelper, private walletKey: string) {}

    public async commitPayment(gameToken: MerchantGameTokenData,
                               request: PaymentRequest,
                               filters: AccountPropertiesFilter[]): Promise<Balance> {

        return this.commitPaymentOperation(gameToken, request, filters, (aGameToken, aRequest) => {
            return this.merchant.commitPayment(aGameToken, aRequest);
        });
    }

    public async commitBetPayment(gameToken: MerchantGameTokenData,
                                  request: PaymentRequest,
                                  filters: AccountPropertiesFilter[]) {

        return this.commitPaymentOperation(gameToken, request, filters, (aGameToken, aRequest) => {
            return this.merchant.commitBetPayment(aGameToken, aRequest);
        });
    }

    public async commitWinPayment(gameToken: MerchantGameTokenData,
                                  request: PaymentRequest,
                                  filters: AccountPropertiesFilter[]): Promise<Balance> {

        return this.commitPaymentOperation(gameToken, request, filters, (aGameToken, aRequest) => {
            return this.merchant.commitWinPayment(aGameToken, aRequest);
        }, true);
    }

    public async registerRound(request: RegisterRoundRequest): Promise<RegisterRoundResponse> {
        return this.merchant.registerRound(request);
    }

    private async commitPaymentOperation(gameToken: MerchantGameTokenData,
                                         request: PaymentRequest,
                                         filters: AccountPropertiesFilter[],
                                         doCommitOperation: (gameToken: MerchantGameTokenData,
                                                             paymentRequest: PaymentRequest) => Promise<Balance>,
                                         isWinRequest: boolean = false)
        : Promise<Balance> {

        let bonusCoinBalance: BonusCoinsBalance;

        let playedInternalFreeBet: boolean = false;
        if (!MerchantPlayer.itIsPromoStoredOnEGPSide(gameToken)) {
            if (gameToken.isPromoInternal) {
                if (request.freeBetCoin && !isWinRequest) {
                    playedInternalFreeBet = true;
                    const filter = filters.find(item => item.account === PLAYER.PLAYER_FREE_BETS_ACCOUNT);
                    request.freeBetBalance = await this.playInternalFreeBet(gameToken,
                        request,
                        filter as FreeBetsAccountFilter
                    );
                } else {
                    const emulateFreeBetBalance = !!((request.freeBetCoin && isWinRequest) || request.freeBetMode);
                    if (!config.skipMidGamePromoCheck || emulateFreeBetBalance) {
                        const walletBalance = await this.getInternalBalance(gameToken.currency,
                            filters,
                            emulateFreeBetBalance);
                        bonusCoinBalance = walletBalance.bonusCoins;
                        request.freeBetBalance = walletBalance.freeBets;
                    }
                }
            }
        } else if (request.freeBetCoin || request.freeBetMode) { // promo stored in EGP db
            // in this case activePromoId and externalId are offered by EGP, and we just forward
            request.freeBetBalance = {
                amount: undefined, // not needed for seamless integration at the moment, can be implemented later if
                                   // EGP will support
                activePromoId: request.promoId,
                externalId: request.externalId,
            };
        }
        try {
            const balance = await doCommitOperation(gameToken, request);
            if (request.freeBetBalance) {
                balance.freeBets = request.freeBetBalance;
            }
            if (bonusCoinBalance) {
                balance.bonusCoins = bonusCoinBalance;
            }
            if (isWinRequest && (request.freeBetCoin || request.freeBetMode)
                && request.roundEnded && !request.freeBetBalance?.amount) {
                await this.closeLastFreeBetRound(
                    request.transactionId.publicId,
                    gameToken.currency,
                    filters
                );
            }
            return balance;
        } catch (err) {
            if (playedInternalFreeBet && err.responseStatus < 500) {
                const filter = filters.find(item => item.account === PLAYER.PLAYER_FREE_BETS_ACCOUNT);
                await this.rollbackInternalFreebet(gameToken, request, filter as FreeBetsAccountFilter);
                if (request.freeBetCoin && !request.freeBetBalance?.amount) {
                    await this.closeLastFreeBetRound(
                        request.transactionId.publicId,
                        gameToken.currency,
                        filters
                    );
                }
            }
            return Promise.reject(err);
        }
    }

    /**
     * This function is telling you if promo is stored in EGP DB.
     */
    public static itIsPromoStoredOnEGPSide(gameTokenData: { gameCode: string }) {
        const egpConfigurations = config.egpConfigurations;
        const gameProviderCode = gameTokenData.gameCode.slice(0, 2).toUpperCase();
        // if EGP is not in the config list by default is stored on skywind side
        return egpConfigurations[gameProviderCode]?.promoLocation === PROMO_LOCATION.EGP;
    }

    public async getBalances(gameToken: MerchantGameTokenData, filters?: AccountPropertiesFilter[]): Promise<Balances> {
        const balance = await this.merchant.getBalances(gameToken);
        if (filters === ONLY_BALANCE_FILTER) {
            return balance;
        }

        const currencies = Object.keys(balance);
        for (const currency of currencies) {
            const sourceBalance = balance[currency];
            const walletBalance = await this.getInternalBalance(gameToken.currency, filters);
            if (walletBalance.extraBalances) {
                sourceBalance.extraBalances = walletBalance.extraBalances;
            }
            if (gameToken.isPromoInternal && walletBalance.freeBets) {
                sourceBalance.freeBets = walletBalance.freeBets;
            }
            if (walletBalance.bonusCoins) {
                sourceBalance.bonusCoins = walletBalance.bonusCoins;
            }
        }

        return balance;
    }

    public getFreeBetInfo(gameToken: MerchantGameTokenData,
                          freeBetRequest: FreeBetInfoRequest,
                          rewards?: PlayerGameFreebet[]): Promise<FreeBetInfo> {
        if (gameToken.isPromoInternal) {
            return this.getInternalFreeBetInfo(gameToken.currency,
                gameToken.gameCode,
                freeBetRequest.stakeAll,
                rewards,
                freeBetRequest.skipCoinValidation);
        } else {
            return this.merchant.getFreeBetInfo(gameToken, freeBetRequest);
        }
    }

    private async getInternalFreeBetInfo(currency: string,
                                         gameCode: string,
                                         stakeAll: number[],
                                         rewards: PlayerGameFreebet[],
                                         skipCoinValidation?: boolean): Promise<FreeBetInfo> {
        const playerWallet: IWallet = await WalletFacade.get(this.walletKey);
        const promos = new PlayerFreebetWallet(playerWallet, currency);
        return promos.getFreebetsInfo(gameCode, stakeAll, rewards, skipCoinValidation);
    }

    private async playInternalFreeBet(gameTokenData: MerchantGameTokenData,
                                      request: PaymentRequest,
                                      filter: FreeBetsAccountFilter): Promise<FreeBetsBalance> {
        try {
            const currency = Currencies.get(gameTokenData.currency);
            const transaction: ITransaction = await WalletFacade.startTransactionWithID(request.transactionId,
                {
                    operationId: OPERATION_ID.FREE_BET,
                    operationName: WALLET_OPERATION_NAME.FREE_BET,
                    gameId: request.roundId.toString(),
                    externalTrxId: request.extTransactionId,
                    params: {
                        gameCode: gameTokenData.gameCode,
                        freeBet: request.freeBetCoin ? currency.toMinorUnits(request.bet) : undefined,
                        freeBetCoin: currency.toMinorUnits(request.freeBetCoin),
                        isTest: gameTokenData.test === undefined ? false : gameTokenData.test,
                        gameSessionId: request.gameSessionId
                    }
                });
            const playerWallet: IWallet = await transaction.getWallet(this.walletKey);
            const promos = new PlayerFreebetWallet(playerWallet, gameTokenData.currency);
            await promos.playFreebet(filter, request.bet, request.freeBetCoin, gameTokenData.gameCode);
            await transaction.commit();

            return promos.getFreebetsBalance(filter.rewards);
        } catch (err) {
            if (err === INSUFFICIENT_BALANCE) {
                if (request.freeBetCoin) {
                    return Promise.reject(new PromoWalletErrors.InsufficientFreebet());
                } else {
                    return Promise.reject(new WalletErrors.InsufficientBalanceError());
                }
            }

            if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new WalletErrors.BadTransactionId());
            }

            if (err === TRANSACTION_EXISTS) {
                const playerWallet: IWallet = await WalletFacade.get(this.walletKey);
                const promos = new PlayerFreebetWallet(playerWallet, gameTokenData.currency);
                return promos.getFreebetsBalance(filter.rewards);
            }

            return Promise.reject(err);
        }
    }

    private async rollbackInternalFreebet(gameTokenData: MerchantGameTokenData,
                                          request: PaymentRequest,
                                          filter: FreeBetsAccountFilter): Promise<FreeBetsBalance> {
        try {
            const currency = Currencies.get(gameTokenData.currency);
            const oldTransaction = await WalletFacade.findCommittedTransaction(request.transactionId,
                OPERATION_ID.FREE_BET);

            const transaction = await WalletFacade.startTransactionWithID(request.transactionId,
                {
                    operationId: OPERATION_ID.FREE_BET_ROLLBACK,
                    operationName: WALLET_OPERATION_NAME.FREE_BET_ROLLBACK,
                    gameId: request.roundId.toString(),
                    externalTrxId: request.extTransactionId,
                    params: {
                        gameCode: gameTokenData.gameCode,
                        freeBet: request.freeBetCoin ? currency.toMinorUnits(request.bet) : undefined,
                        freeBetCoin: currency.toMinorUnits(request.freeBetCoin),
                        isTest: gameTokenData.test === undefined ? false : gameTokenData.test
                    }
                });

            const playerWallet: IWallet = await transaction.getWallet(this.walletKey);
            const promos = new PlayerFreebetWallet(playerWallet, gameTokenData.currency);
            await promos.rollbackFreebet(oldTransaction);
            await transaction.commit();

            return promos.getFreebetsBalance(filter.rewards);
        } catch (err) {
            if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new WalletErrors.BadTransactionId());
            }

            if (err === TRANSACTION_EXISTS) {
                const playerWallet: IWallet = await WalletFacade.get(this.walletKey);
                const promos = new PlayerFreebetWallet(playerWallet, gameTokenData.currency);
                return promos.getFreebetsBalance(filter.rewards);
            }

            return Promise.reject(err);
        }

    }

    public async getInternalBalance(currency: string,
                                    filters: AccountPropertiesFilter[],
                                    emulateFreeBetBalance: boolean = false): Promise<Balance> {
        const playerWallet: IWallet = await WalletFacade.get(this.walletKey);
        const result: Balance = { main: 0 };

        for (const filter of filters) {
            const account: IAccount = playerWallet.accounts.get(filter.account);
            if (account.name === PLAYER.PLAYER_FREE_BETS_ACCOUNT) {
                const freeBets = new PlayerFreebetWallet(playerWallet, currency)
                    .getFreebetsBalance((filter as FreeBetsAccountFilter).rewards, emulateFreeBetBalance);
                if (freeBets) {
                    result.freeBets = freeBets;
                }
            } else if (account.name === PLAYER.PLAYER_BONUS_COINS_ACCOUNT) {
                const bonusCoins = new PlayerBonusCoinWallet(playerWallet, currency)
                    .getBalance((filter as BonusCoinsAccountFilter).reward);
                if (bonusCoins) {
                    result.bonusCoins = bonusCoins;
                }
            } else {
                filter.properties.forEach(property => {
                    const amount = account.get(property.name) as number;
                    // Ignore main balance
                    if (property.name !== PLAYER.PLAYER_BALANCE) {
                        if (amount) {
                            if (!result.extraBalances) {
                                result.extraBalances = {};
                            }
                            result.extraBalances[property.name] = property.fromWalletValue(amount);
                        }
                    }
                });
            }
        }
        return result;
    }

    public async closeLastFreeBetRound(
        transactionId: string,
        currency: string,
        filters: AccountPropertiesFilter[]
    ): Promise<void> {
        const transaction = await WalletFacade.startTransactionWithID(transactionId, {
            operationId: OPERATION_ID.LAST_FREE_BET_ROUND_CLOSE,
            operationName: WALLET_OPERATION_NAME.LAST_FREE_BET_ROUND_CLOSE,
            force: true
        });
        const playerWallet = await transaction.getWallet(this.walletKey);
        const promos = new PlayerFreebetWallet(playerWallet, currency);
        await promos.closeLastFreeBetRound(filters);
        await transaction.commit();
    }

    public async commitBonusPayment(gameToken: MerchantGameTokenData, request: PaymentRequest,
                                    filters: AccountPropertiesFilter[]): Promise<Balance> {
        return this.commitPaymentOperation(gameToken, request, filters, (aGameToken, aRequest) => {
            return this.merchant.commitBonusPayment(aGameToken, aRequest);
        });
    }

    public async commitOfflineBonusPayment(payment: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return this.merchant.commitOfflineBonusPayment(payment);
    }
}
